#!/usr/bin/env python3
"""
测试 Tool_web2OBSpreview.py 中新增的 player_waiting_order 功能
验证命令行处理和演示序列是否正确包含等待订单提示
"""

import sys
import re
from pathlib import Path

def test_preview_tool_modifications():
    """测试预览工具的修改是否正确"""
    print("=== 测试 Tool_web2OBSpreview.py 的 player_waiting_order 功能 ===")
    
    # 读取工具文件内容
    tool_file = Path('Tool_web2OBSpreview.py')
    if not tool_file.exists():
        print("✗ Tool_web2OBSpreview.py 文件不存在")
        return False
    
    with open(tool_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("✓ 工具文件读取成功")
    
    # 测试1: 检查是否添加了 waitorder 命令处理
    waitorder_pattern = r"elif cmd in \['waitorder', 'wo'\]:"
    if re.search(waitorder_pattern, content):
        print("✓ 找到 waitorder 命令处理逻辑")
    else:
        print("✗ 缺少 waitorder 命令处理逻辑")
        return False
    
    # 测试2: 检查 waitorder 命令是否正确发送 player_waiting_order 事件
    waitorder_action_pattern = r"'status_type': 'player_waiting_order'"
    if re.search(waitorder_action_pattern, content):
        print("✓ waitorder 命令正确发送 player_waiting_order 事件")
    else:
        print("✗ waitorder 命令未正确发送 player_waiting_order 事件")
        return False
    
    # 测试3: 检查帮助信息是否包含 waitorder 说明
    help_pattern = r"waitorder <玩家名>\s*-\s*显示等待订单提示区域"
    if re.search(help_pattern, content):
        print("✓ 帮助信息包含 waitorder 命令说明")
    else:
        print("✗ 帮助信息缺少 waitorder 命令说明")
        return False
    
    # 测试4: 检查演示序列是否包含等待订单提示
    demo_pattern = r'"等待订单提示".*player_waiting_order'
    if re.search(demo_pattern, content, re.DOTALL):
        print("✓ 演示序列包含等待订单提示")
    else:
        print("✗ 演示序列缺少等待订单提示")
        return False
    
    # 测试5: 检查演示序列中等待订单提示的位置是否合理
    # 应该在更新队列之后，玩家开始游戏之前
    demo_steps_pattern = r'demo_steps = \[(.*?)\]'
    demo_match = re.search(demo_steps_pattern, content, re.DOTALL)
    if demo_match:
        demo_content = demo_match.group(1)
        steps = re.findall(r'\("([^"]+)"', demo_content)
        
        if "等待订单提示" in steps:
            wait_index = steps.index("等待订单提示")
            queue_index = steps.index("更新队列") if "更新队列" in steps else -1
            playing_index = steps.index("玩家开始游戏") if "玩家开始游戏" in steps else -1
            
            if queue_index < wait_index < playing_index:
                print("✓ 等待订单提示在演示序列中的位置合理")
            else:
                print(f"! 等待订单提示位置: {wait_index}, 更新队列: {queue_index}, 玩家开始游戏: {playing_index}")
                print("! 等待订单提示的位置可能不够理想，但功能正常")
        else:
            print("✗ 演示序列中未找到等待订单提示步骤")
            return False
    else:
        print("! 无法解析演示序列结构，跳过位置检查")
    
    # 测试6: 检查是否保持了原有功能的完整性
    original_commands = ['1', '2', '3', 'msg', 'queue', 'grab', 'clear', 'demo', 'reload']
    missing_commands = []
    
    for cmd in original_commands:
        if cmd == 'demo':
            pattern = f"if cmd == '{cmd}'"  # demo 使用 if 而不是 elif
        elif cmd in ['1', '2', '3']:
            pattern = f"elif cmd in \\['1', '2', '3'\\]"
            if re.search(pattern, content):
                continue  # 只需要检查一次
        else:
            pattern = f"elif cmd == '{cmd}'"

        if not re.search(pattern, content):
            missing_commands.append(cmd)
    
    if not missing_commands:
        print("✓ 所有原有命令功能保持完整")
    else:
        print(f"✗ 缺少原有命令: {missing_commands}")
        return False
    
    print("\n=== 功能验证总结 ===")
    print("✓ waitorder/wo 命令处理逻辑正确")
    print("✓ 帮助信息已更新")
    print("✓ 演示序列已包含等待订单提示")
    print("✓ 原有功能保持完整")
    print("✓ 所有修改符合预期要求")
    
    return True

def test_command_parsing():
    """测试命令解析逻辑"""
    print("\n=== 测试命令解析逻辑 ===")
    
    # 模拟命令解析
    test_commands = [
        "waitorder",
        "waitorder 测试玩家",
        "wo",
        "wo 玩家B",
        "WaitOrder 大写测试",  # 测试大小写
    ]
    
    for cmd_str in test_commands:
        parts = cmd_str.split()
        cmd = parts[0].lower()
        player_name = parts[1] if len(parts) > 1 else "等待玩家"
        
        if cmd in ['waitorder', 'wo']:
            expected_command = {
                'action': 'show_status_message',
                'status_type': 'player_waiting_order',
                'player_name': player_name
            }
            print(f"✓ '{cmd_str}' -> 玩家名: '{player_name}'")
        else:
            print(f"✗ '{cmd_str}' -> 命令未识别")
            return False
    
    print("✓ 命令解析逻辑测试通过")
    return True

if __name__ == "__main__":
    print("开始测试 Tool_web2OBSpreview.py 的 player_waiting_order 功能...")
    
    # 测试工具修改
    tool_ok = test_preview_tool_modifications()
    
    # 测试命令解析
    parsing_ok = test_command_parsing()
    
    if tool_ok and parsing_ok:
        print("\n🎉 所有测试通过！Tool_web2OBSpreview.py 已成功集成 player_waiting_order 功能。")
        print("\n📋 使用方法:")
        print("1. 运行 python Tool_web2OBSpreview.py")
        print("2. 输入 'waitorder 玩家名' 或 'wo 玩家名' 测试等待订单提示")
        print("3. 输入 'demo' 查看包含等待订单提示的完整演示序列")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，请检查修改。")
        sys.exit(1)
