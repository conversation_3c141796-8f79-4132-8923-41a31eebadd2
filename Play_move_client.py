import socket
import json
import time
import threading
import logging
import uuid
import queue
# --- 关键修改：导入 MutableMapping ---
from typing import Dict, Optional, Any, MutableMapping

logger = logging.getLogger(__name__)

class MoveServiceClient:
    """
    客户端，用于连接 Move_Main.py 的游戏服务并与之交互。
    """
    def __init__(self, host: str, port: int, status_update_queue: queue.Queue, stop_flag: MutableMapping[str, bool]):
        self.host = host
        self.port = port
        self.status_update_queue = status_update_queue
        self.stop_flag = stop_flag
        self.client_socket: Optional[socket.socket] = None
        self._socket_file = None
        self.is_connected = False
        self.receive_thread: Optional[threading.Thread] = None
        self.active_commands: Dict[str, Dict[str, Any]] = {} # command_id -> {player_id, player_name, target_object_id, etc.}
        self.lock = threading.Lock()
        self.reconnect_delay = 1.0
        self.max_reconnect_delay = 30.0
        self.last_connection_state = None  # 新增：记录上次连接状态
        self._last_error_log_time = 0  # 新增

    def _connect_socket(self) -> bool:
        try:
            if self.client_socket:
                self.client_socket.close()
            if self._socket_file:
                self._socket_file.close()

            self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.client_socket.settimeout(5.0) # Connection timeout
            self.client_socket.connect((self.host, self.port))
            self.client_socket.settimeout(1.0) # Receive timeout
            # 移除 makefile，直接使用 socket
            self._socket_file = None
            self.is_connected = True
            logger.info(f"成功连接到移动服务 {self.host}:{self.port}")
            self.reconnect_delay = 1.0 # Reset reconnect delay on successful connection
            # 新增：只有状态变化时才通知
            if self.last_connection_state != 'connected':
                self.status_update_queue.put({
                    'type': 'move_service_event',
                    'event_name': 'move_service_reconnected',
                    'data': {}
                })
                self.last_connection_state = 'connected'
            return True
        except socket.timeout:
            logger.error(f"连接移动服务超时: {self.host}:{self.port}")
        except ConnectionRefusedError:
            logger.error(f"移动服务连接被拒绝: {self.host}:{self.port}")
        except Exception as e:
            logger.error(f"连接移动服务失败: {e}")
        
        self.is_connected = False
        if self.client_socket:
            self.client_socket.close()
            self.client_socket = None
        if self._socket_file:
            self._socket_file.close()
            self._socket_file = None
        # 新增：只有状态变化时才通知
        if self.last_connection_state != 'disconnected':
            self.status_update_queue.put({
                'type': 'move_service_event',
                'event_name': 'move_service_disconnected',
                'data': {}
            })
            self.last_connection_state = 'disconnected'
        return False

    def _ensure_connection(self):
        if not self.is_connected:
            return self._connect_socket()
        return True

    def send_pick_command(self, target_object_id: str, player_id: str, player_name: str, z_offset_extra: float = 0.0) -> Optional[str]:
        if not self._ensure_connection():
            logger.error("无法发送抓取指令：未连接到移动服务。")
            return None

        command_id = str(uuid.uuid4())
        request_payload = {
            "type": "command",
            "action": "pick_object_by_id",
            "command_id": command_id,
            "params": {
                "object_id": target_object_id,
                "player_id": player_id,
                "player_name": player_name,
                "z_offset_extra": z_offset_extra
            }
        }
        try:
            with self.lock:
                self.active_commands[command_id] = {
                    "player_id": player_id,
                    "player_name": player_name,
                    "requested_object_id": target_object_id,
                    "status": "sent",
                    "send_time": time.time()
                }
            
            json_payload = json.dumps(request_payload) + '\n'
            self.client_socket.sendall(json_payload.encode('utf-8'))
            logger.info(f"已发送抓取指令到移动服务: ReqID={command_id}, Player={player_name}, Target={target_object_id}, Z_Offset_Extra={z_offset_extra}")
            
            # 不再等待同步响应，全部交由事件接收线程处理
            # 乐观地返回command_id，事件接收线程将处理所有后续消息
            return command_id
        
        except (socket.error, BrokenPipeError) as e:
            logger.error(f"发送抓取指令时发生套接字错误: {e}")
            with self.lock:
                if command_id in self.active_commands:
                    del self.active_commands[command_id]
            # --- 新增：强制断开连接，触发自动重连 ---
            self.disconnect()
            return None
            
        except Exception as e:
            logger.error(f"发送抓取指令时发生未知错误: {e}")
            with self.lock:
                if command_id in self.active_commands:
                    del self.active_commands[command_id]
            # --- 新增：未知错误也断开连接，防止假死 ---
            self.disconnect()
            return None

    def _receive_events_loop(self):
        """事件接收线程现在将处理所有类型的消息，包括指令响应和事件"""
        logger.info("移动服务事件接收线程已启动。")
        recv_buffer = ""
        while self.stop_flag.get('running', True):
            if not self.is_connected:
                if not self.stop_flag.get('running', True): 
                    break
                logger.info(f"移动服务连接断开，将在 {self.reconnect_delay:.1f} 秒后尝试重连...")
                time.sleep(self.reconnect_delay)
                self.reconnect_delay = min(self.reconnect_delay * 1.5, self.max_reconnect_delay)
                # 新增：只有状态变化时才通知
                if self.last_connection_state != 'disconnected':
                    self.status_update_queue.put({
                        'type': 'move_service_event',
                        'event_name': 'move_service_disconnected',
                        'data': {}
                    })
                    self.last_connection_state = 'disconnected'
                if not self._connect_socket():
                    recv_buffer = ""
                    continue
            try:
                data = self.client_socket.recv(4096)
                if not data:
                    logger.warning("移动服务关闭了连接。")
                    self.disconnect()
                    recv_buffer = ""
                    # 新增：只有状态变化时才通知
                    if self.last_connection_state != 'disconnected':
                        self.status_update_queue.put({
                            'type': 'move_service_event',
                            'event_name': 'move_service_disconnected',
                            'data': {}
                        })
                        self.last_connection_state = 'disconnected'
                    continue

                recv_buffer += data.decode('utf-8', 'ignore')
                while '\n' in recv_buffer:
                    line, recv_buffer = recv_buffer.split('\n', 1)
                    line = line.strip()
                    if not line:
                        continue

                    motion_event_data = json.loads(line)
                    logger.debug(f"从移动服务收到消息: {motion_event_data}")

                    event_type = motion_event_data.get('type')
                    if event_type == 'response':
                        # 处理指令响应
                        command_id = motion_event_data.get('command_id')
                        status = motion_event_data.get('status')
                        message = motion_event_data.get('message', '')
                        
                        with self.lock:
                            if command_id in self.active_commands:
                                if status == 'queued':
                                    logger.info(f"抓取指令 {command_id} 已被移动服务接受并加入队列")
                                    self.active_commands[command_id]['status'] = 'queued'
                                else:
                                    logger.error(f"抓取指令 {command_id} 被移动服务拒绝: {message}")
                                    del self.active_commands[command_id]
                            else:
                                logger.warning(f"收到未知指令ID的响应: {command_id}")
                                
                    elif event_type == 'event': # Ensure it's an event, not a response meant for send_pick_command
                        # 首先从事件数据中获取 request_id
                        request_id = motion_event_data.get('request_id')
                        motion_event_name = motion_event_data.get('event_name')
                        motion_event_payload = motion_event_data.get('data', {})

                        # 添加详细的事件接收日志，现在包含 request_id
                        logger.info(f"[事件接收] 收到运动模块事件: event_name='{motion_event_name}', request_id={request_id}")
                        logger.debug(f"[事件接收] 事件载荷: {motion_event_payload}")

                        player_details = {}
                        with self.lock:
                            if request_id in self.active_commands:
                                player_details = self.active_commands[request_id]
                            else:
                                logger.warning(f"收到未知 request_id 的事件: {request_id}. 事件: {motion_event_data}")

                        player_id = player_details.get('player_id')
                        player_name = player_details.get('player_name')
                        requested_object_id = player_details.get('requested_object_id')

                        logger.debug(f"[事件接收] 玩家详情: player_id={player_id}, player_name={player_name}, requested_object_id={requested_object_id}")

                        # --- 关键修改：处理统一的抓取状态事件 ---
                        if motion_event_name == 'object_picked':  # 新的统一事件名
                            # 从移动模块的数据中获取抓取状态信息
                            success = motion_event_payload.get('success', False)
                            object_id = motion_event_payload.get('object_id')
                            message = motion_event_payload.get('message', '')

                            # 根据成功状态确定物品名称：成功时使用message中的物体名称，失败时设为nothing
                            if success:
                                item_name_from_motion = message  # 成功时message直接包含物体名称
                            else:
                                item_name_from_motion = "nothing"  # 失败时设为nothing

                            logger.info(f"[事件转换] 检测到object_picked事件，success: {success}, object_id: {object_id}, 物品名称: '{item_name_from_motion}', 原始消息: '{message}'")

                            unified_event = {
                                'type': 'object_picked',  # 统一的事件类型
                                'request_id': request_id,
                                'player_id': player_id,
                                'player_name': player_name,
                                'item_name': item_name_from_motion,
                                'success': success,  # 添加成功标志
                                'object_id': object_id,  # 添加物品ID
                                'message': message,  # 添加原始消息
                                'source': 'real_mode'  # 标记事件来源
                            }
                            self.status_update_queue.put(unified_event)
                            logger.info(f"[事件发送] 已转换并发送统一 'object_picked' 事件: {unified_event}")
                        
                        elif motion_event_name == 'cycle_completed':
                            update_payload_for_cycle = {
                                'type': 'move_service_event',  # 改为统一使用move_service_event类型
                                'request_id': request_id,
                                'player_id': player_id,
                                'player_name': player_name,
                                'event_name': motion_event_name,  # 'cycle_completed'
                                'data': motion_event_payload
                            }
                            self.status_update_queue.put(update_payload_for_cycle)
                            
                            # If cycle_completed, remove from active_commands
                            with self.lock:
                                if request_id in self.active_commands:
                                    del self.active_commands[request_id]
                                    logger.info(f"移动服务请求 {request_id} (cycle_completed) 已完成，从活动命令中移除。")
                        
                        else:  # 其他运动模块事件 (e.g., 'move_started', 'error')
                            logger.info(f"[事件转换] 处理其他运动模块事件: '{motion_event_name}'")
                            other_event_payload = {
                                'type': 'move_service_event',
                                'request_id': request_id,
                                'player_id': player_id,
                                'player_name': player_name,
                                'requested_object_id': requested_object_id,
                                'event_name': motion_event_name,
                                'data': motion_event_payload
                            }
                            self.status_update_queue.put(other_event_payload)
                            logger.debug(f"[事件发送] 通用移动服务事件 '{motion_event_name}' 已发送: {other_event_payload}")
                    
                    elif event_type == 'response':
                        # This case should ideally be handled by the synchronous part of send_pick_command
                        logger.warning(f"事件循环收到一个响应消息（可能未被同步处理）: {motion_event_data}")

            except socket.timeout:
                # 只会在没有数据时抛 timeout，继续循环
                time.sleep(0.05)  # 短暂休眠避免空转
                continue

            except json.JSONDecodeError as e:
                logger.error(f"解析移动服务事件JSON失败: {e}, 数据: '{line if 'line' in locals() else 'N/A'}'")
                continue

            except (ConnectionResetError, BrokenPipeError, socket.error) as e:
                now = time.time()
                if now - getattr(self, "_last_error_log_time", 0) > 1:
                    logger.warning(f"移动服务事件接收时连接错误（忽略，保持长连）: {e}")
                    self._last_error_log_time = now
                recv_buffer = ""  # 清空缓冲区
                # 新增：只有状态变化时才通知
                if self.last_connection_state != 'disconnected':
                    self.status_update_queue.put({
                        'type': 'move_service_event',
                        'event_name': 'move_service_disconnected',
                        'data': {}
                    })
                    self.last_connection_state = 'disconnected'
                continue

            except Exception as e:
                logger.error(f"移动服务事件接收时发生未知错误: {e}")
                recv_buffer = ""  # 清空缓冲区

        logger.info("移动服务事件接收线程已停止。")
        self.disconnect()

    def start(self):
        if not self._connect_socket():
             logger.warning("首次连接移动服务失败，事件接收线程可能不会立即运行。")
        
        self.receive_thread = threading.Thread(target=self._receive_events_loop, daemon=True, name="MoveServiceEventReceiver")
        self.receive_thread.start()

    def disconnect(self):
        logger.info("正在断开与移动服务的连接...")
        self.is_connected = False # Set this first
        if self._socket_file:
            try:
                self._socket_file.close()
            except Exception as e:
                logger.debug(f"关闭套接字文件对象时出错: {e}")
            self._socket_file = None
        if self.client_socket:
            try:
                self.client_socket.shutdown(socket.SHUT_RDWR)
            except (OSError, socket.error) as e:
                logger.debug(f"关闭套接字时出错 (shutdown): {e}")
            try:
                self.client_socket.close()
            except Exception as e:
                logger.debug(f"关闭套接字时出错 (close): {e}")
            self.client_socket = None
        logger.info("与移动服务的连接已断开。")

    def stop(self):
        logger.info("正在停止移动服务客户端...")
        self.disconnect() # Disconnect first
        if self.receive_thread and self.receive_thread.is_alive():
            try:
                self.receive_thread.join(timeout=2.0)
                if self.receive_thread.is_alive():
                    logger.warning("移动服务事件接收线程未在2秒内停止。")
            except Exception as e:
                logger.error(f"等待移动服务事件接收线程停止时出错: {e}")
        logger.info("移动服务客户端已停止。")
