import tkinter as tk
from tkinter import ttk
import tkinter.font as tkfont
import threading
import time
import queue
from datetime import datetime
import logging
from multiprocessing import Queue as MpQueue # 关键修改：导入 multiprocessing.Queue
from typing import Dict, Any, Optional

class PlayDisplayer:
    """负责创建和管理一个独立的GUI窗口，用于实时显示游戏直播助手的关键信息"""
    
    # --- 关键修改：更新构造函数签名 ---
    def __init__(self, config: Dict[str, Any], update_queue: MpQueue, stop_flag: Dict[str, bool]):
        self.logger = logging.getLogger(__name__)
        
        # 配置
        self.config = config
        self.displayer_config = config.get('displayer', {})
        
        # --- 关键修改：接收多进程队列和停止标志 ---
        self.update_event_queue = update_queue
        self.stop_flag = stop_flag
        # --- 修改结束 ---

        # 内部状态
        self.active_players_lines = []
        self.is_running = True
        self.current_target_object = None
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title(self.displayer_config.get('window', {}).get('title', "游戏助手实时信息面板"))
        
        # 设置窗口大小和位置
        width_ratio = self.displayer_config.get('window', {}).get('default_width_ratio', 0.75)
        height_ratio = self.displayer_config.get('window', {}).get('default_height_ratio', 0.6)
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = int(screen_width * width_ratio)
        window_height = int(screen_height * height_ratio)
        
        x_pos = (screen_width - window_width) // 2
        y_pos = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x_pos}+{y_pos}")
        self.root.update_idletasks()
        
        bg_color = self.displayer_config.get('window', {}).get('background_color', "#DDDDDD")
        self.root.configure(bg=bg_color)
        self.bg_color = bg_color
        
        # 创建左右分栏
        self.paned_window = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True)
        
        self.left_frame = ttk.Frame(self.paned_window)
        self.right_frame = ttk.Frame(self.paned_window)
        
        self.paned_window.add(self.left_frame, weight=1)
        self.paned_window.add(self.right_frame, weight=1)
        
        #left_panel_ratio = self.displayer_config.get('layout', {}).get('left_panel_ratio', 0.6)
        #self.paned_window.sashpos(0, int(window_width * left_panel_ratio))
        # 修正方案: 强制更新布局后，使用 paned_window 的真实宽度
        self.root.update_idletasks()
        left_panel_ratio = self.displayer_config.get('layout', {}).get('left_panel_ratio', 0.6)
        actual_width = self.paned_window.winfo_width()
        self.paned_window.sashpos(0, int(actual_width * left_panel_ratio))
        # 初始化UI组件
        self._init_panels()
        self._load_fonts_and_colors()
        
        # --- 关键修改：移除本地状态变量的重复初始化 ---
        # 这些变量现在由主进程管理，并通过队列事件传递
        self.player_info = {}
        self.player_games = {}
        self.current_player = {}
        # --- 修改结束 ---
        
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        self.logger.info("GUI窗口已初始化")
    
    def _init_panels(self):
        """初始化左右面板的UI组件"""
        # 左侧面板
        self.current_player_text = tk.Text(self.left_frame, wrap=tk.WORD, state=tk.DISABLED, height=4,
                                          bg=self.bg_color, bd=0, highlightthickness=0)
        self.current_player_text.pack(fill=tk.X, padx=10, pady=5)
        
        self.caught_item_label = tk.Label(self.left_frame, text="", anchor="w", justify=tk.LEFT, bg=self.bg_color)
        self.caught_item_label.pack(fill=tk.X, padx=10, pady=5)
        
        # 创建状态信息框架：排队人数和FPS并列显示
        self.status_frame = ttk.Frame(self.left_frame)
        self.status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.queue_size_label = tk.Label(self.status_frame, text="排队: 0", anchor="w", bg=self.bg_color)
        self.queue_size_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.fps_label = tk.Label(self.status_frame, text="FPS: --", anchor="e", bg=self.bg_color)
        self.fps_label.pack(side=tk.RIGHT, fill=tk.X, expand=True)
        
        # 排队玩家列表
        self.queue_frame = ttk.Frame(self.left_frame)
        self.queue_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.queue_text = tk.Text(self.queue_frame, wrap=tk.WORD, state=tk.DISABLED, height=10,
                                  bg="#FFFFFF", fg="#000000")  # 修改：设置白色背景和黑色文字
        self.queue_yscroll = ttk.Scrollbar(self.queue_frame, orient=tk.VERTICAL, command=self.queue_text.yview)
        self.queue_text.configure(yscrollcommand=self.queue_yscroll.set)
        self.queue_xscroll = ttk.Scrollbar(self.queue_frame, orient=tk.HORIZONTAL, command=self.queue_text.xview)
        self.queue_text.configure(xscrollcommand=self.queue_xscroll.set)
        
        self.queue_text.grid(row=0, column=0, sticky="nsew")
        self.queue_yscroll.grid(row=0, column=1, sticky="ns")
        self.queue_xscroll.grid(row=1, column=0, sticky="ew")
        
        self.queue_frame.grid_rowconfigure(0, weight=1)
        self.queue_frame.grid_columnconfigure(0, weight=1)
        
        # 右侧面板
        self.log_frame = ttk.Frame(self.right_frame)
        self.log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.log_text = tk.Text(self.log_frame, wrap=tk.WORD, state=tk.DISABLED, height=10)
        self.log_yscroll = ttk.Scrollbar(self.log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=self.log_yscroll.set)
        
        self.log_text.grid(row=0, column=0, sticky="nsew")
        self.log_yscroll.grid(row=0, column=1, sticky="ns")
        
        self.log_frame.grid_rowconfigure(0, weight=1)
        self.log_frame.grid_columnconfigure(0, weight=1)
    
    def _load_fonts_and_colors(self):
        """根据配置加载字体和颜色，组合基础字体和区域特定设置。"""
        self.fonts = {}
        font_configs = self.displayer_config.get('fonts', {})
        bg_color = self.displayer_config.get('window', {}).get('background_color', "#2B2B2B")

        try:
            # --- 1. 加载基础字体族和字重 ---
            player_font_base = font_configs.get('player_font', {})
            player_family = player_font_base.get('family', 'Segoe UI Emoji Regular')
            player_weight = player_font_base.get('weight', 'normal')

            data_font_base = font_configs.get('data_font', {})
            data_family = data_font_base.get('family', 'Microsoft YaHei UI')
            data_weight = data_font_base.get('weight', 'normal')

            # --- 新增：加载全局玩家名颜色 ---
            global_colors = font_configs.get('global_colors', {})
            player_name_color = global_colors.get('player_name', '#eb24c1') # 默认为粉红色

            # --- 2. 为每个UI区域创建并应用字体和颜色 ---

            # a) 当前玩家区域 (左上)
            cp_config = font_configs.get('current_player', {})
            cp_font = tkfont.Font(family=data_family, size=cp_config.get('size', 14), weight=data_weight)
            cp_color = cp_config.get('color', '#FFFFFF')
            self.current_player_text.configure(font=cp_font, bg=bg_color)
            # 为玩家名和普通文本分别配置标签
            self.current_player_text.tag_configure("current_player_name", font=cp_font, foreground=player_name_color)
            self.current_player_text.tag_configure("current_player_data", font=cp_font, foreground=cp_color)

            # b) 状态信息区域 (抓到物品, 排队人数, FPS)
            si_config = font_configs.get('status_info', {})
            si_font = tkfont.Font(family=data_family, size=si_config.get('size', 12), weight=data_weight)
            si_color = si_config.get('color', '#CCCCCC')
            for widget in [self.caught_item_label, self.queue_size_label, self.fps_label]:
                widget.configure(font=si_font, fg=si_color, bg=bg_color)

            # c) 排队列表 (左下)
            ql_config = font_configs.get('queue_list', {})
            ql_size = ql_config.get('size', 12)
            ql_color = ql_config.get('color', '#E0E0E0')
            
            # 为排队列表创建两种字体（大小相同，family不同）
            queue_player_font = tkfont.Font(family=player_family, size=ql_size, weight=player_weight)
            queue_data_font = tkfont.Font(family=data_family, size=ql_size, weight=data_weight)
            
            self.queue_text.configure(bg=bg_color)
            # 玩家名使用 player_font，颜色使用全局玩家名颜色
            self.queue_text.tag_configure("queue_player_name", font=queue_player_font, foreground=player_name_color)
            # 其他数据使用 data_font
            self.queue_text.tag_configure("queue_data", font=queue_data_font, foreground=ql_color)

            # d) 活动日志 (右侧)
            al_config = font_configs.get('activity_log', {})
            al_size = al_config.get('size', 11)
            al_colors = al_config.get('colors', {})
            
            # 为活动日志创建两种字体（大小相同，family不同）
            log_player_font = tkfont.Font(family=player_family, size=al_size, weight=player_weight)
            log_data_font = tkfont.Font(family=data_family, size=al_size, weight=data_weight)

            log_comment_color = al_colors.get('comment_text', '#98FB98')
            log_default_color = al_colors.get('default_text', '#E0E0E0')

            self.log_text.configure(bg=bg_color)
            # 玩家名使用 player_font 和 全局玩家名颜色
            self.log_text.tag_configure("log_player_name", font=log_player_font, foreground=player_name_color)
            # 评论使用 player_font 和特定颜色
            self.log_text.tag_configure("log_comment_text", font=log_player_font, foreground=log_comment_color)
            # 普通文本使用 data_font 和特定颜色
            self.log_text.tag_configure("log_default_text", font=log_data_font, foreground=log_default_color)

        except Exception as e:
            self.logger.error(f"加载字体和配置标签时出错: {e}", exc_info=True)
    
    def _format_line_for_display(self, data_dict, field_widths_config, prefix=""):
        """通用文本行格式化"""
        formatted_parts = []
        for field, value in data_dict.items():
            #if field == 'target_object':
            #    continue
            
            # 确保所有值都是字符串类型
            value_str = str(value) if value is not None else ""
            
            if field == 'name':
                # 检查是否为当前玩家的显示，如果是，则应用截断逻辑
                if prefix == "current_player":
                    max_len = field_widths_config.get('max_active_name_length', 10)
                    formatted_value = self._truncate_text(value_str, max_len)
                else:
                    # 对其他地方（如排队列表）的name字段保留原有的ljust逻辑
                    width_key = 'name_width'
                    prefixed = f"{prefix}_{width_key}" if prefix else width_key
                    width = field_widths_config.get(prefixed, len(value_str))
                    formatted_value = value_str.ljust(width)
            elif field == 'history_rewards':
                formatted_value = value_str
            else:
                formatted_value = value_str
            
            formatted_parts.append(formatted_value)
        return "，".join(formatted_parts)
    
    def _update_current_player_display(self, data=None):
        """更新当前玩家信息的显示"""
        try:
            self.current_player_text.configure(state=tk.NORMAL)
            self.current_player_text.delete(1.0, tk.END)

            if data and data.get('player_id'):
                player_id = data.get('player_id')
                history_for_player = data.get('history_for_player')
                details = self._get_formatted_player_details(player_id, is_current_player=True, history_data=history_for_player)
                
                player_name = details.get('name', '')
                if "VirtualPlayer" in player_id:
                    player_name = f"[托]{player_name}"

                target_id = details.get('target_id', '')
                target_object = details.get('target_object', '未知')
                target_display = f"目标: {target_id}({target_object})"
                
                comments_count = f"评论: {details.get('comments_count', 0)}"
                games_played = f"游戏次数: {details.get('games_played', 0)}"
                history_rewards = f"历史奖励: {details.get('history_rewards', '')}"

                # --- 修改：分段插入并应用标签 ---
                self.current_player_text.insert(tk.END, player_name, "current_player_name")
                
                data_part = f"，{target_display}，{comments_count}，{games_played}，{history_rewards}"
                self.current_player_text.insert(tk.END, data_part, "current_player_data")
            
            self.current_player_text.configure(state=tk.DISABLED)
        except Exception as e:
            self.logger.error(f"Error updating current player display: {e}")
            # 确保即使出错也能禁用文本框
            if self.current_player_text.winfo_exists():
                self.current_player_text.configure(state=tk.DISABLED)

    def _update_caught_item_display(self, item_name=None, coupon_text=None):
        """更新抓取到的物品和优惠券提示的显示"""
        try:
            if item_name:
                base_text = f"抓到: {item_name}"
                if coupon_text:
                    # 将配置中的\n转换为GUI中实际的换行
                    coupon_text_formatted = coupon_text.replace("\n", " ")
                    self.caught_item_label.configure(text=f"{base_text}；{coupon_text_formatted}")
                else:
                    self.caught_item_label.configure(text=base_text)
            else:
                self.caught_item_label.configure(text="")
        except Exception as e:
            self.logger.error(f"Error updating caught item display: {e}")
    
    def _update_queue_size_display(self, queue_size=0):
        """更新排队人数的显示"""
        try:
            self.queue_size_label.configure(text=f"当前排队人数: {queue_size}")
        except Exception as e:
            self.logger.error(f"Error updating queue size display: {e}")
    
    def _update_fps_display(self):
        """更新FPS显示（已弃用，保留用于兼容性）"""
        # 这个方法现在主要用于初始化显示，实际FPS数据通过队列接收
        try:
            self.fps_label.configure(text="FPS: --")
        except Exception as e:
            self.logger.error(f"Error updating FPS display: {e}")
            self.fps_label.configure(text="FPS: ??")

    def _update_fps_display_from_tcp(self, fps, data_age):
        """从队列接收的FPS数据更新显示"""
        try:
            # 检查数据新鲜度，超过2秒认为过期
            if data_age <= 2.0:
                self.fps_label.configure(text=f"FPS: {fps:.1f}")
            else:
                self.fps_label.configure(text="FPS: --")
        except Exception as e:
            self.logger.error(f"Error updating FPS display: {e}")
            self.fps_label.configure(text="FPS: ??")
    
    def _update_player_queue_display(self, queue_data=None, player_info_data=None):
        """更新排队玩家列表的显示，使用不同字体"""
        try:
            self.queue_text.configure(state=tk.NORMAL)
            self.queue_text.delete(1.0, tk.END)

            if not queue_data: # 如果 queue_data 是 None 或者空列表
                self.queue_text.insert(tk.END, "排队列表为空\n", "queue_data")
                self.queue_text.configure(state=tk.DISABLED)
                return
                
            for i, queue_item in enumerate(queue_data):
                try:
                    # queue_item 现在是 (entry_list, possibility, priority[, order_id])
                    if len(queue_item) == 4:
                        entry, possibility, priority, order_id = queue_item
                    else:
                        entry, possibility, priority = queue_item
                        order_id = None
                    
                    # 直接使用索引访问player_id，避免解包整个entry
                    if len(entry) >= 4:
                        player_id = entry[3]
                        name = entry[2]
                    else:
                        continue  # 跳过格式不正确的条目
                    
                    if player_id:
                        player_data = player_info_data.get(player_id, {})
                        target_id = entry[4] if len(entry) > 4 else ""
                        target_object = entry[6] if len(entry) > 6 else f"未知({target_id})"
                        comments_count = player_data.get('comments_after_game', 0)
                        
                        # 计算停留时长
                        stay_duration_min = 1
                        come_time_str = player_data.get('come_time')
                        if come_time_str:
                            try:
                                come_time = datetime.strptime(come_time_str, "%a %b %d %H:%M:%S %Y")
                                stay_duration_min = max(1, int((datetime.now() - come_time).total_seconds() / 60))
                            except (ValueError, TypeError):
                                pass
                        
                        # 如果是付费玩家，在名字前加标记
                        display_name = name
                        # 如果是虚拟玩家，在名字前加[托]标记
                        if "VirtualPlayer" in player_id:
                            display_name = f"[托]{display_name}"
                        elif order_id is not None:
                            display_name = f"[付费]{display_name}"
                        
                        # --- 修改开始：分段插入并应用标签 ---
                        self.queue_text.insert(tk.END, f"{i+1}：", "queue_data")
                        self.queue_text.insert(tk.END, f"{display_name} ", "queue_player_name")
                        
                        data_part = f"目标:{target_id}({target_object}) 评:{comments_count} 停:{stay_duration_min}分\n"
                        self.queue_text.insert(tk.END, data_part, "queue_data")
                        # --- 修改结束 ---
                except (ValueError, IndexError, TypeError) as e:
                    self.logger.error(f"GUI_DISPLAYER: 处理队列项目 {i} 时出错: {e}, queue_item: {str(queue_item)[:100]}", exc_info=True)
                    continue
            
            self.queue_text.configure(state=tk.DISABLED)
        except Exception as e:
            self.logger.error(f"GUI_DISPLAYER: Error updating player queue display: {e}", exc_info=True)

    def _add_active_player_event(self, event_data):
        """添加玩家活动事件到右侧日志，并为特定字段应用颜色和字体。"""
        try:
            player_id = event_data.get('player_id')
            if not player_id: return

            max_name_length = self.displayer_config.get('text_formatting', {}).get('max_active_name_length', 10)
            max_comment_length = self.displayer_config.get('text_formatting', {}).get('max_active_comment_length', 50)
            
            player_name = self._truncate_text(event_data.get('player_name', ''), max_name_length)
            comment_text = self._truncate_text(event_data.get('comment_text', ''), max_comment_length)
            total_comments = event_data.get('total_comments', 0)
            total_games = event_data.get('total_games', 0)
            stay_duration_min = event_data.get('stay_duration_min', 1)

            self.log_text.configure(state=tk.NORMAL)
            
            # 从后往前插入，逻辑更清晰
            self.log_text.insert('1.0', '\n')
            self.log_text.insert('1.0', comment_text, ("log_comment_text",))
            
            default_part = f"，评{total_comments}，停{stay_duration_min}分，玩{total_games}，评："
            self.log_text.insert('1.0', default_part, ("log_default_text",))
            
            self.log_text.insert('1.0', player_name, ("log_player_name",))

            # 限制最大行数
            max_lines = self.displayer_config.get('active_players_max_lines', 30)
            num_lines = int(self.log_text.index('end-1c').split('.')[0])
            if num_lines > max_lines:
                self.log_text.delete(f"{max_lines+1}.0", tk.END)

            self.log_text.configure(state=tk.DISABLED)

        except Exception as e:
            self.logger.error(f"Error adding active player event: {e}", exc_info=True)
    
    def _get_formatted_player_details(self, player_id, is_current_player=False, game_content=None, history_data=None):
        """获取玩家详细信息"""
        result = {'name': '', 'target_id': '', 'target_object': '', 'comments_count': '0', 'duration': '', 'games_played': '0', 'history_rewards': ''}
        
        try:
            # 获取玩家基本信息
            if player_id in self.player_info:
                player_data_info = self.player_info[player_id]
                result['name'] = player_data_info.get('name', '')
                result['comments_count'] = str(player_data_info.get('comments_after_game', 0))
            
            # --- 修改开始：优先使用传入的 history_data ---
            history_rewards = []
            games_to_process = []
            if history_data is not None:
                # 如果直接传入了历史数据（来自game_start事件），则使用它
                games_to_process = history_data
            else:
                # 否则，回退到从本地 self.player_games 获取
                if player_id in self.player_games:
                    games_to_process = self.player_games[player_id]
            
            result['games_played'] = str(len(games_to_process))
            for game in games_to_process:
                if len(game) > 1 and game[1] and game[1].lower() != 'nothing':
                    history_rewards.append(game[1])
            # --- 修改结束 ---
            
            max_rewards_length = self.displayer_config.get('text_formatting', {}).get('max_history_rewards_length', 50)
            result['history_rewards'] = self._truncate_text("、".join(history_rewards), max_rewards_length)
            
            # 处理目标ID和目标物体名称
            if is_current_player:
                target_id = self.current_player.get('target_id', '')
                result['target_id'] = str(target_id)
                
                if self.current_target_object:
                    result['target_object'] = self.current_target_object
                else:
                    target_object = self.current_player.get('target_object', '')
                    result['target_object'] = target_object if target_object else (f"未知({target_id})" if target_id else "")
            elif game_content:
                # game_content 现在是 (entry_list, possibility, priority[, order_id])
                try:
                    if isinstance(game_content, tuple) and len(game_content) >= 1:
                        entry = game_content[0]
                        if isinstance(entry, list) and len(entry) >= 5:
                            # 使用直接索引访问，避免解包长度问题
                            target_id = entry[4]  # content字段
                            result['target_id'] = str(target_id)
                            # 从entry中获取物体名称（如果有的话）
                            if len(entry) >= 7:
                                result['target_object'] = entry[6]  # target_object_name字段
                            else:
                                result['target_object'] = f"未知({target_id})"
                except (IndexError, TypeError) as e:
                    self.logger.error(f"解析游戏内容时出错: {e}")
                    result['target_object'] = "未知"
            
            return result
        except Exception as e:
            self.logger.error(f"Error getting formatted player details: {e}")
            return result
    
    def _truncate_text(self, text, max_len):
        """截断文本以确保它不超过最大长度"""
        if not text:
            return ""
        if len(text) <= max_len:
            return text
        return text[:max_len-3] + "..."
    
    def _process_gui_updates(self):
        """处理来自主应用程序的GUI更新事件"""
        try:
            if not self.stop_flag.get('running', True) or not self.is_running:
                return
            
            try:
                # 修改：处理队列中所有可用的更新，而不是每次只处理一个
                updates_processed = 0
                while updates_processed < 10:  # 每次最多处理10个更新，避免阻塞UI线程太久
                    try:
                        event = self.update_event_queue.get_nowait()
                        event_type = event.get('type')
                        # 降低日志级别，避免频繁输出
                        if event_type not in ['queue_update', 'fps_update']:
                            self.logger.debug(f"Received GUI update event: {event_type}")
                        
                        if event_type == 'queue_update':
                            # --- 关键修改：更新本地数据副本 ---
                            self.player_info = event.get('player_info_data', {})
                            self.player_games = event.get('player_games_data', {})
                            # --- 修改结束 ---
                            self._update_queue_size_display(event.get('queue_size', 0))
                            self._update_player_queue_display(event.get('queue_data'), self.player_info)
                        elif event_type == 'game_start':
                            self.current_player = {
                                'player_id': event.get('player_id', ''),
                                'name': event.get('name', ''),
                                'target_id': event.get('target_id', ''),
                                'target_object': event.get('target_object', ''),
                            }
                            self.current_target_object = event.get('target_object', '')
                            self._update_current_player_display(event)
                            # --- 优化：显式传递None以清除物品显示，保持代码一致性 ---
                            self._update_caught_item_display(None)
                            # --- 修改结束 ---
                        elif event_type == 'game_end':
                            self.current_player.clear()
                            self.current_target_object = None
                            self._update_current_player_display()
                            self._update_caught_item_display()
                        elif event_type == 'object_picked':
                            item_name = event.get('item_name', '')
                            coupon_text = event.get('coupon_text')  # 获取新增的优惠文本
                            self._update_caught_item_display(item_name, coupon_text)
                        elif event_type == 'new_comment':
                            self._add_active_player_event(event)
                        elif event_type == 'fps_update':
                            self._update_fps_display_from_tcp(event.get('fps', 0), event.get('data_age', 99))
                        
                        updates_processed += 1
                    except queue.Empty:
                        break  # 队列空了，退出循环
                
                if self.stop_flag.get('running', True) and self.is_running:
                    self.root.after(10, self._process_gui_updates)
            except Exception as e:
                self.logger.error(f"处理GUI更新时出错: {e}", exc_info=True)
                if self.stop_flag.get('running', True) and self.is_running:
                    self.root.after(10, self._process_gui_updates)
        except Exception as e:
            self.logger.error(f"GUI更新处理外层错误: {e}", exc_info=True)
            if self.stop_flag.get('running', True) and self.is_running:
                self.root.after(10, self._process_gui_updates)
    
    def _on_closing(self):
        """处理窗口关闭事件"""
        self.logger.info("GUI窗口关闭")
        self.is_running = False
        self.root.destroy()
    
    def run(self):
        """启动GUI"""
        self.logger.info("启动GUI")
        
        # 初始化显示
        self._update_queue_size_display()
        self._update_player_queue_display()
        self._update_current_player_display()
        # 下面代码会安排在10毫秒后执行 _process_gui_updates，
        # 而 _process_gui_updates 内部会再次调用 root.after，从而形成一个持续的循环。
        self.root.after(10, self._process_gui_updates)
        self.root.mainloop()

# --- 关键修改：移除 DisplayerTCPServer 和 DisplayerClient 类 ---

# --- 关键修改：更新启动函数以接收队列和停止标志 ---
def start_displayer_process(config: Dict[str, Any], update_queue: MpQueue, stop_flag: Dict[str, bool]):
    """启动独立的GUI进程，并直接使用传入的队列"""
    logger = None
    
    try:
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - [%(threadName)s] - %(levelname)s - %(message)s'
        )
        
        logger = logging.getLogger(__name__)
        logger.info("GUI进程启动")
        
        # 创建GUI，直接传入队列和停止标志
        displayer = PlayDisplayer(config, update_queue, stop_flag)
        
        # 运行GUI主循环
        displayer.run()
        
    except Exception as e:
        if logger:
            logger.error(f"GUI进程出错: {e}", exc_info=True)
        else:
            print(f"GUI进程出错: {e}")
    finally:
        # 当GUI窗口关闭时，也设置主进程的停止标志
        stop_flag['running'] = False
        if logger:
            logger.info("GUI进程结束")
        else:
            print("GUI进程结束")

# --- 关键修改：更新入口点逻辑 ---
if __name__ == "__main__":
    # 此脚本现在不应直接运行，而是由 play_main 通过 Process 启动
    print("此文件应该通过主程序(play_main.py)作为子进程启动，而不是直接运行。")
