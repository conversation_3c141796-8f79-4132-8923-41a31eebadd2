#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版虚拟玩家插入时机验证脚本
"""

import sqlite3

def main():
    print("虚拟玩家插入时机验证")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('Play_db.db')
        cursor = conn.cursor()
        
        # 获取所有游戏记录
        cursor.execute('SELECT player_id, game_time FROM games ORDER BY game_time ASC')
        games = cursor.fetchall()
        
        print(f"总游戏记录数: {len(games)}")
        
        # 统计真实玩家和虚拟玩家
        real_count = 0
        virtual_count = 0
        
        print("\n最近20条游戏记录:")
        for i, (player_id, game_time) in enumerate(games[-20:]):
            is_virtual = 'Virtual' in player_id
            if is_virtual:
                virtual_count += 1
                player_type = "虚拟"
            else:
                real_count += 1
                player_type = "真实"
            print(f"{i+1:2d}. {game_time} - {player_type} - {player_id[:25]}")
        
        print(f"\n统计:")
        print(f"真实玩家游戏数: {real_count}")
        print(f"虚拟玩家游戏数: {virtual_count}")
        
        # 检查当前队列状态
        cursor.execute('SELECT COUNT(*) FROM queue WHERE player_id NOT LIKE "Virtual%"')
        current_real_players = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM queue')
        total_queue = cursor.fetchone()[0]
        
        print(f"\n当前队列状态:")
        print(f"真实玩家: {current_real_players}")
        print(f"总队列人数: {total_queue}")
        print(f"虚拟玩家: {total_queue - current_real_players}")
        
        # 判断模式
        if current_real_players <= 2:
            print(f"\n当前模式: 填充模式")
            print(f"填充模式特征: 虚拟玩家持续填充队列，保持活跃度")
        else:
            print(f"\n当前模式: 平衡/稀疏模式")
            print(f"应该按比例插入虚拟玩家")
        
        conn.close()
        print("\n验证完成")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
