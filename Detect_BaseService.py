"""
YOLO检测系统的通用组件。读取Config_Detect.yaml和Config_Detect_Cam.yaml配置文件，
包含网络通信、结果处理等共用功能，被Detect_Client.py调用
# 作为服务运行（无窗口）
python Detect_BaseService.py --as-service

# 带可视化窗口运行
python Detect_BaseService.py

# 指定参数运行
python Detect_BaseService.py --camera 0 --port 5555 --as-service

输入： 摄像头视频流 
输出：
网络服务监听指定端口
终端显示检测结果
可视化窗口（如果启用）
"""
import cv2
# 替换 import supervision as sv 为具体组件导入
from supervision.detection.core import Detections
from supervision.annotators.core import BoxAnnotator, LabelAnnotator 
# 修复 Color 的导入路径
from supervision.draw.color import Color

import torch
import numpy as np
import time
from collections import defaultdict
import yaml
import os
import io
import Detect_ROI  # 导入ROI处理工具模块
from Detect_Tracker import ObjectTracker  # 导入跟踪器模块
import threading
import socket
import json
import argparse
import queue
import multiprocessing
from concurrent.futures import ThreadPoolExecutor
import sys
from collections import defaultdict
import traceback  # 添加traceback模块用于异常调试

# 确保导入透视变换相关函数
from Detect_ROI import absolute_to_normalized_perspective, calculate_perspective_transform_matrix

# 加载配置文件
def load_config(config_path='Config_Detect.yaml'):
    with open(config_path, 'r', encoding='utf-8') as file:
        config = yaml.safe_load(file)
    return config

# 加载摄像头位置和ROI配置
def load_cam_pos(config_path='Config_Detect_Cam.yaml'):
    with open(config_path, 'r', encoding='utf-8') as file:
        config = yaml.safe_load(file)
    return config

# 加载配置
config = load_config()
cam_config = load_cam_pos()

# 设置多线程推理
torch.set_num_threads(config['performance']['torch_threads'])

# 从配置文件加载尺寸限制
SIZE_LIMITS = {int(k): v for k, v in config['detection']['size_limits'].items()}

# 加载类别名称映射
CLASS_NAMES = {int(k): v for k, v in config['classes'].items()}

# 加载置信度阈值
CONFIDENCE_THRESHOLDS = {int(k): v for k, v in config['detection']['confidence_thresholds'].items()}

# 默认置信度阈值
DEFAULT_CONFIDENCE_THRESHOLD = config['detection']['default_confidence']

# 调试配置
DEBUG = config['debug']['enabled']

# 跟踪配置
TRACKING_ENABLED = config['tracker'].get('enabled', True)  # 读取跟踪开关
TRACKING_THRESHOLD = config['tracker']['tracking_threshold']
MIN_DETECTION_FRAMES = config['tracker']['min_detection_frames'] 
DUPLICATE_THRESHOLD = config['tracker']['duplicate_threshold']
MAX_MISSING_FRAMES = config['tracker']['max_missing_frames']

# 网络配置
NETWORK_HOST = config['network'].get('host', 'localhost')
NETWORK_PORT = config['network'].get('port', 5555)

# 摄像头配置
CAMERA_WIDTH = config['camera']['width']
CAMERA_HEIGHT = config['camera']['height']
DISPLAY_SCALE = config['camera'].get('display_scale', 1.0) # 加载显示缩放比例，默认为1.0

class ClientManager:
    """客户端连接管理器，处理订阅客户端的连接和数据推送"""
    def __init__(self):
        self.clients = {}  # {client_id: socket}
        self.clients_lock = threading.Lock()
        self.next_client_id = 1
        
    def add_client(self, client_socket, addr):
        """添加新的客户端连接"""
        with self.clients_lock:
            client_id = self.next_client_id
            self.next_client_id += 1
            self.clients[client_id] = client_socket
            print(f"客户端 {client_id} 已连接，地址：{addr}")
            return client_id
    
    def remove_client(self, client_id):
        """移除客户端连接"""
        with self.clients_lock:
            if (client_id in self.clients):
                client_socket = self.clients.pop(client_id)  # 获取并移除
                try:
                    client_socket.shutdown(socket.SHUT_RDWR)  # 尝试优雅关闭
                except OSError:
                    pass  # 可能已经关闭
                finally:
                    try:
                        client_socket.close()  # 确保关闭
                    except OSError:
                        pass  # 可能已经关闭
                print(f"客户端 {client_id} 已断开")
    
    def broadcast_results(self, detection_results):
        """向所有订阅客户端推送检测结果，包含归一化坐标"""
        if not detection_results:
            return
            
        # 转换为推送格式
        push_data = {
            "status": "data_updated",
            "data": {
                "timestamp": detection_results.get("timestamp", time.time()),
                "fps": detection_results.get("fps", 0),
                "process_time_ms": detection_results.get("process_time_ms", 0),
                "objects": []
            }
        }
        
        # 转换物体信息，使用新的归一化边界框坐标
        for obj in detection_results.get("objects", []):
            push_obj = {
                "track_number": obj.get("track_number"),
                "class_id": obj.get("class_id"),
                "class_name": obj.get("class_name"),
                "center_abs_pixel": obj.get("center_abs_pixel", [0, 0]),  # 保留绝对坐标用于调试
                "center_normalized_prop": obj.get("center_normalized_prop", [None, None]),
                "confidence": obj.get("confidence", 0.0),
                "bounding_box_xyxy_normalized_prop": obj.get("bounding_box_xyxy_normalized_prop", [None, None, None, None])  # 新的归一化边界框字段
            }
            push_data["data"]["objects"].append(push_obj)
        
        message = json.dumps(push_data) + '\n'
        message_bytes = message.encode('utf-8')
        
        # 获取当前客户端列表的副本
        with self.clients_lock:
            current_clients = list(self.clients.items())
        
        # 向每个客户端发送数据（在锁外进行以避免阻塞）
        disconnected_clients = []
        for client_id, client_socket in current_clients:
            try:
                client_socket.sendall(message_bytes)
            except Exception as e:
                print(f"向客户端 {client_id} 推送数据失败: {e}")
                disconnected_clients.append(client_id)
        
        # 移除断开的客户端
        for client_id in disconnected_clients:
            self.remove_client(client_id)

class DetectionServiceBase:
    """
    检测服务基类，定义通用接口和实现通用功能
    纯接口+工具类：只提供检测和辅助功能，不包含任何单实例模式的循环逻辑
    """
    def __init__(self, camera_id=None, show_window=True, enable_tracker=True):
        """
        初始化检测服务基类
        
        参数:
            camera_id: 摄像头ID（仅供子类单实例测试使用，多实例框架不使用）
            show_window: 是否显示窗口（仅供子类单实例测试使用，多实例框架不使用）
            enable_tracker: 是否启用跟踪器
        """
        self.model = None  # 将在子类中初始化
        # 更新标注器配置
        self.bounding_box_annotator = BoxAnnotator(thickness=1)
        self.label_annotator = LabelAnnotator(
            text_scale=0.4,
            text_thickness=1,
            text_padding=2,
            color=Color.BLACK
        )
        
        # 初始化跟踪器（如果启用）
        if enable_tracker and TRACKING_ENABLED:
            tracker_config = {
                'size_limits': SIZE_LIMITS,
                'class_names': CLASS_NAMES,
                'confidence_thresholds': CONFIDENCE_THRESHOLDS,
                'default_confidence': DEFAULT_CONFIDENCE_THRESHOLD,
                'debug': DEBUG,
                'tracking_threshold': TRACKING_THRESHOLD,
                'min_detection_frames': MIN_DETECTION_FRAMES,
                'duplicate_threshold': DUPLICATE_THRESHOLD,
                'max_missing_frames': MAX_MISSING_FRAMES
            }
            self.tracker = ObjectTracker(tracker_config)
        else:
            self.tracker = None
            
        # ROI配置
        self.roi_points = np.array(cam_config['roi_points'])
        self.roi_mask = None # 为预计算的ROI掩码添加缓存
        
        # --- 使用透视变换进行归一化 ---
        self.perspective_transform_matrix = None
        # ROI的4个角点对应到归一化正方形的4个角点
        # 顺序：左下(0,0), 右下(1,0), 右上(1,1), 左上(0,1)
        self.normalized_target_roi_corners = np.array([
            [0, 0],  # 对应 roi_points[0] 左下角
            [1, 0],  # 对应 roi_points[1] 右下角  
            [1, 1],  # 对应 roi_points[2] 右上角
            [0, 1]   # 对应 roi_points[3] 左上角
        ], dtype=np.float32)

        if len(self.roi_points) == 4:
            try:
                self.perspective_transform_matrix = Detect_ROI.calculate_perspective_transform_matrix(
                    self.roi_points.tolist(),
                    self.normalized_target_roi_corners.tolist()
                )
                if self.perspective_transform_matrix is None:
                    print(f"[{self.__class__.__name__}] 警告: 无法计算透视变换矩阵。归一化坐标将不可用。")
            except Exception as e:
                print(f"[{self.__class__.__name__}] 初始化透视变换矩阵时发生错误: {e}")
                self.perspective_transform_matrix = None
        else:
            print(f"[{self.__class__.__name__}] 错误: ROI点数量不为4 ({len(self.roi_points)}个点)，无法使用透视变换。归一化坐标将不可用。")
            self.perspective_transform_matrix = None

        self.CLASS_NAMES = CLASS_NAMES
        
        print(f"[{self.__class__.__name__}] 初始化完成，enable_tracker={enable_tracker and TRACKING_ENABLED}")
        if self.perspective_transform_matrix is not None:
            print(f"    透视变换矩阵已预计算。")
        else:
            print(f"    警告: 透视变换矩阵未成功计算，归一化坐标可能不准确或不可用。")

    @staticmethod
    def format_object_label(class_name, track_number=None, norm_x=None, norm_y=None):
        """通用的对象标签格式化方法，使用归一化坐标"""
        label = class_name
        if track_number is not None:
            label += f" #{track_number}"
        if norm_x is not None and norm_y is not None:
            label += f" ({norm_x:.2f},{norm_y:.2f})"
        return label
    
    @staticmethod
    def _static_extract_objects_info(detections, class_names_map, 
                                     roi_points_abs_for_norm,
                                     precomputed_perspective_matrix_for_norm):
        """
        从检测结果中提取对象信息的核心逻辑，包含基于透视变换的归一化坐标和边界框
        """
        objects_info = []
        if detections is not None and len(detections) > 0:
            for i in range(len(detections)):
                class_id = detections.class_id[i]
                class_name = class_names_map.get(class_id, f"class_{class_id}")
                
                xyxy = detections.xyxy[i]
                x1, y1, x2, y2 = xyxy
                center_x = (x1 + x2) / 2
                center_y = (y1 + y2) / 2
                
                # --- 关键修复：在转换类型前检查值是否为None ---
                tracker_number = None
                if hasattr(detections, 'data') and 'tracker_number' in detections.data:
                    raw_tracker_number = detections.data['tracker_number'][i]
                    if raw_tracker_number is not None:
                        tracker_number = int(raw_tracker_number)

                # 使用透视变换计算归一化中心坐标
                normalized_center_coords = None
                if precomputed_perspective_matrix_for_norm is not None:
                    normalized_center_coords = absolute_to_normalized_perspective(
                        (center_x, center_y),
                        precomputed_perspective_matrix_for_norm
                    )
                
                # 计算归一化边界框坐标
                normalized_bbox_coords = [None, None, None, None]
                if precomputed_perspective_matrix_for_norm is not None:
                    # 获取边界框的左上角和右下角点
                    top_left_point = (x1, y1)
                    bottom_right_point = (x2, y2)
                    
                    # 进行透视变换
                    norm_top_left = absolute_to_normalized_perspective(
                        top_left_point, precomputed_perspective_matrix_for_norm
                    )
                    norm_bottom_right = absolute_to_normalized_perspective(
                        bottom_right_point, precomputed_perspective_matrix_for_norm
                    )
                    
                    # 如果变换成功，更新归一化边界框坐标
                    if norm_top_left and norm_bottom_right:
                        normalized_bbox_coords = [
                            norm_top_left[0],    # norm_x1
                            norm_top_left[1],    # norm_y1  
                            norm_bottom_right[0], # norm_x2
                            norm_bottom_right[1]  # norm_y2
                        ]
                
                obj_info = {
                    "track_number": tracker_number,
                    "class_id": int(class_id),
                    "class_name": class_name,
                    "center_abs_pixel": [float(center_x), float(center_y)],
                    "center_normalized_prop": [float(c) for c in normalized_center_coords] if normalized_center_coords else [None, None],
                    "confidence": float(detections.confidence[i]),
                    "bounding_box_xyxy_abs": [float(x1), float(y1), float(x2), float(y2)],  # 保留绝对坐标以供内部使用
                    "bounding_box_xyxy_normalized_prop": [float(c) if c is not None else None for c in normalized_bbox_coords]  # 新的归一化边界框字段
                }
                objects_info.append(obj_info)
        return objects_info

    @staticmethod
    def _static_annotate_objects_on_frame(
        frame_to_annotate, 
        detections_to_annotate,
        box_annotator_instance,
        label_annotator_instance,
        class_names_map,
        precomputed_perspective_matrix_for_label,
        draw_center_points=True,
        center_point_color=(0, 255, 255)
    ):
        """通用的物体标注核心逻辑，使用基于透视变换的归一化坐标"""
        annotated_frame = frame_to_annotate.copy()
        labels = []
        
        if detections_to_annotate is not None and len(detections_to_annotate) > 0:
            for i in range(len(detections_to_annotate)):
                class_id = detections_to_annotate.class_id[i]
                class_name = class_names_map.get(class_id, f"class_{class_id}")
                
                xyxy = detections_to_annotate.xyxy[i]
                x1, y1, x2, y2 = xyxy
                center_x = (x1 + x2) / 2
                center_y = (y1 + y2) / 2
                
                # --- 关键修复：在转换类型前检查值是否为None ---
                tracker_number = None
                if hasattr(detections_to_annotate, 'data') and 'tracker_number' in detections_to_annotate.data:
                    raw_tracker_number = detections_to_annotate.data['tracker_number'][i]
                    if raw_tracker_number is not None:
                        tracker_number = int(raw_tracker_number)

                # 使用透视变换计算归一化比例坐标用于标签
                normalized_coords_label = None
                if precomputed_perspective_matrix_for_label is not None:
                    normalized_coords_label = Detect_ROI.absolute_to_normalized_perspective(
                        (center_x, center_y),
                        precomputed_perspective_matrix_for_label
                    )

                label_text = DetectionServiceBase.format_object_label(
                    class_name, 
                    tracker_number, 
                    normalized_coords_label[0] if normalized_coords_label else None,
                    normalized_coords_label[1] if normalized_coords_label else None
                )
                labels.append(label_text)
                
                if draw_center_points:
                    center_point = (int(center_x), int(center_y))
                    cv2.circle(annotated_frame, center_point, 3, center_point_color, -1)
            
            if box_annotator_instance:
                annotated_frame = box_annotator_instance.annotate(
                    scene=annotated_frame, detections=detections_to_annotate
                )
            if label_annotator_instance and labels:
                annotated_frame = label_annotator_instance.annotate(
                    scene=annotated_frame, 
                    detections=detections_to_annotate,
                    labels=labels
                )
        
        return annotated_frame

    def _extract_objects_info(self, filtered_detections):
        """从检测结果中提取对象信息（使用静态方法并传入预计算的透视变换矩阵）"""
        return self._static_extract_objects_info(
            filtered_detections,
            self.CLASS_NAMES,
            self.roi_points.tolist(),
            self.perspective_transform_matrix
        )

    def _annotate_image(self, frame, filtered_detections):
        """使用通用标注核心逻辑，并传入预计算的透视变换矩阵"""
        return self._static_annotate_objects_on_frame(
            frame_to_annotate=frame,
            detections_to_annotate=filtered_detections,
            box_annotator_instance=self.bounding_box_annotator,
            label_annotator_instance=self.label_annotator,
            class_names_map=self.CLASS_NAMES,
            precomputed_perspective_matrix_for_label=self.perspective_transform_matrix,
            draw_center_points=True,
            center_point_color=(0, 255, 255)
        )

def create_detection_service(camera_id=None, show_window=True, enable_tracker=True):
    """
    创建检测服务实例
    
    参数:
        camera_id: 摄像头ID（仅供子类单实例测试使用，多实例框架不使用）
        show_window: 是否显示窗口（仅供子类单实例测试使用，多实例框架不使用）
        enable_tracker: 是否启用跟踪器
    
    返回:
        检测服务实例
    """
    # 读取模型类型
    model_type = config.get('model', {}).get('type', 'yolov10-detect')
    
    # 根据模型类型动态创建服务实例
    print(f"创建检测服务: model_type={model_type}, enable_tracker={enable_tracker}")
    if model_type == 'yolov8-seg':
        from Detect_YOLOV8seg import SegmentationService
        # 确保传递的参数名称正确匹配 SegmentationService 构造函数签名
        return SegmentationService(
            camera_id=camera_id,
            show_window=show_window,
            enable_tracker=enable_tracker
        )
    else:
        from Detect_YoloV10Detc import DetectionService
        return DetectionService(
            camera_id=camera_id,
            show_window=show_window,
            enable_tracker=enable_tracker
        )

class ParallelDetectionManager:
    """并行检测管理器，协调多个检测实例进行并行处理"""
    def __init__(self, num_instances, show_window=True, camera_id=None, port=None):
        # 首先设置窗口相关属性
        self.show_window = show_window
        # 根据参数自定义窗口标题
        if camera_id is not None:
            self.window_title = f"Detection Cam {camera_id}"
        elif port is not None:
            self.window_title = f"Detection Port {port}"
        else:
            self.window_title = "ParallelDetection"
        
        # 确保至少有2个工作实例
        if num_instances < 2:
            print(f"警告: ParallelDetectionManager配置的实例数({num_instances})小于2，将强制使用2个实例。")
            self.num_instances = 2
        else:
            self.num_instances = num_instances
            
        self.camera_id = camera_id if camera_id is not None else cam_config['camera_id']
        
        # 并行处理配置
        self.frame_buffer_size = config.get('parallel_processing', {}).get('frame_buffer_size', 10)
        self.result_buffer_size = config.get('parallel_processing', {}).get('result_buffer_size', 10)
        self.worker_timeout = config.get('parallel_processing', {}).get('worker_timeout', 5.0)
        
        # 队列和缓冲区
        self.frame_queue = queue.Queue(maxsize=self.frame_buffer_size)
        self.result_queue = queue.Queue(maxsize=self.result_buffer_size)
        self.result_buffer = {}  # {frame_id: result_data}
        self.next_frame_id = 0
        self.next_expected_frame_id = 0
        
        # 全局跟踪器 - 使用配置参数
        tracker_config = {
            'size_limits': {int(k): v for k, v in config['detection']['size_limits'].items()},
            'class_names': {int(k): v for k, v in config['classes'].items()},
            'confidence_thresholds': {int(k): v for k, v in config['detection']['confidence_thresholds'].items()},
            'default_confidence': config['detection']['default_confidence'],
            'debug': config['debug']['enabled'],
            'tracking_threshold': config['tracker']['tracking_threshold'],
            'min_detection_frames': config['tracker']['min_detection_frames'],
            'duplicate_threshold': config['tracker']['duplicate_threshold'],
            'max_missing_frames': config['tracker']['max_missing_frames']
        }
        self.global_tracker = ObjectTracker(tracker_config)
        
        # 工作实例和线程
        self.worker_instances = []
        self.worker_threads = []
        self.frame_reader_thread = None
        self.result_processor_thread = None
        self.server_thread = None
        
        # 摄像头
        self.cap = None
        
        # 运行状态
        self.running = False
        
        """ # 显示和标注组件
        self.bounding_box_annotator = sv.BoxAnnotator()
        self.label_annotator = sv.LabelAnnotator(
            text_position=sv.Position.TOP_LEFT,
            text_scale=0.5,
            # text_color=sv.Color.BLACK,
            text_thickness=1,
            text_padding=5
        )
        """
        # FPS计算
        self.fps = 0
        self.frame_count = 0
        self.start_time = time.time()
        self.process_time = 0
        
        # 通信端口和最新结果
        self.port = port if port is not None else NETWORK_PORT
        self.server_socket = None
        self.latest_results = {}
        self.results_lock = threading.Lock()
        
        # ROI配置
        self.roi_points = np.array(cam_config['roi_points'])
        self.roi_mask = None # 为预计算的ROI掩码添加缓存
        
        # --- 使用透视变换进行归一化 ---
        self.perspective_transform_matrix = None
        self.normalized_target_roi_corners = np.array([
            [0, 0], [1, 0], [1, 1], [0, 1]  # 左下, 右下, 右上, 左上
        ], dtype=np.float32)

        if len(self.roi_points) == 4:
            try:
                self.perspective_transform_matrix = Detect_ROI.calculate_perspective_transform_matrix(
                    self.roi_points.tolist(),
                    self.normalized_target_roi_corners.tolist()
                )
                if self.perspective_transform_matrix is None:
                    print(f"[PDM] 警告: 无法计算PDM的透视变换矩阵。归一化坐标将不可用。")
            except Exception as e:
                print(f"[PDM] 初始化PDM的透视变换矩阵时发生错误: {e}")
                self.perspective_transform_matrix = None
        else:
            print(f"[PDM] 错误: ROI点数量不为4 ({len(self.roi_points)}个点)，PDM无法使用透视变换。归一化坐标将不可用。")
            self.perspective_transform_matrix = None
        
        if self.perspective_transform_matrix is not None:
            print(f"[PDM] 透视变换矩阵已预计算。")
        else:
            print(f"[PDM] 警告: PDM的透视变换矩阵未成功计算。")
        
        # 添加客户端管理器
        self.client_manager = ClientManager()
        
        # 添加显示数据共享
        self.display_frame = None
        self.display_lock = threading.Lock()
        # 新增：标记是否已收到第一次带标注的帧
        self.annotation_ready = False
        
        # 保存显示缩放比例
        self.display_scale = DISPLAY_SCALE
        
        # PDM特有的标注器配置
        self.bounding_box_annotator = BoxAnnotator(thickness=2) # , color=sv.Color.RED
        self.label_annotator = LabelAnnotator(
            text_scale=0.5,
            text_thickness=1,
            text_padding=3,
            color=Color.BLACK  # 使用color代替text_color和text_background_color
        )
        
        print(f"初始化并行检测管理器: {num_instances} 个工作实例")
    
    def _init_camera(self):
        """初始化摄像头"""
        print(f"[PDM] 初始化摄像头: ID={self.camera_id}, 分辨率={CAMERA_WIDTH}x{CAMERA_HEIGHT}")
        self.cap = cv2.VideoCapture(self.camera_id)
        # 添加对 self.cap 的空值检查
        if self.cap is None:
            print("[PDM] 警告: 无法创建摄像头捕获对象")
            return False
            
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, CAMERA_WIDTH)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, CAMERA_HEIGHT)
        # 修复尝试在 None 对象上调用 read 方法的问题
        ret, frame = self.cap.read() if self.cap is not None else (False, None)
        if not ret or frame is None:
            print("[PDM] 警告: 无法从摄像头读取首帧")
            return False
            
        print(f"[PDM] 摄像头初始化成功，帧大小: {frame.shape}")
        return True
    
    def _create_worker_instances(self):
        """创建工作实例"""
        print("[PDM] 开始创建工作实例...")
        for i in range(self.num_instances):
            # 创建检测服务实例，不显示窗口，不启用跟踪器
            # 重要: 与单实例模式相反，多实例模式下工作实例不需要跟踪器和窗口
            service_instance = create_detection_service(
                camera_id=None,  # 多实例模式下，工作实例不需要摄像头
                show_window=False,  # 多实例模式下，工作实例不显示窗口
                enable_tracker=False  # 多实例模式下，工作实例不需要跟踪器
            )
            self.worker_instances.append(service_instance)
            print(f"[PDM] 创建工作实例 {i+1}/{self.num_instances}, 类型: {type(service_instance).__name__}")
                
    def _frame_reader_loop(self):
        """帧读取循环"""
        print("[PDM] 帧读取线程启动")
        frame_count = 0
        
        while self.running:
            try:
                ret, frame = self.cap.read()
                if not ret or frame is None:
                    print("[PDM] 警告：无法读取帧")
                    time.sleep(0.1)  # 短暂等待后重试
                    continue
                
                # 实时预览原始帧（检测结果到来前显示）
                if self.show_window and not self.annotation_ready:
                    with self.display_lock:
                        self.display_frame = frame.copy()
                
                # 添加帧读取成功日志（每20帧输出一次减少日志量）
                if frame_count % 20 == 0:
                    print(f"[PDM] 成功读取第 {frame_count} 帧")
                
                # 将帧和ID放入队列
                try:
                    self.frame_queue.put((frame.copy(), frame_count)) # 使用阻塞模式，使用copy避免引用问题
                    frame_count += 1
                except queue.Full: # 理论上阻塞模式不应到此
                    print("[PDM] 警告: 帧队列已满，丢弃当前帧")
                    continue
            except Exception as e:
                print(f"[PDM] 帧读取错误: {e}")
                traceback.print_exc()
                break
        
        print("[PDM] 帧读取线程结束")
    
    def _worker_task(self, worker_id, service_instance):
        """工作线程任务"""
        print(f"--- [DIAG] 工作线程 {worker_id} 启动")
        task_count = 0
        
        while self.running:
            try:
                # 从队列获取帧
                print(f"--- [DIAG] Worker {worker_id}: Waiting for frame...")
                frame, frame_id = self.frame_queue.get(timeout=self.worker_timeout)
                print(f"--- [DIAG] Worker {worker_id}: Got frame {frame_id}")
                
                if frame is None:  # 收到停止信号
                    print(f"[PDM] 工作线程 {worker_id} 收到停止信号")
                    break
                
                # 每20帧输出一次日志，以减少日志量
                if task_count % 20 == 0:
                    print(f"[PDM] 工作线程 {worker_id} 开始处理第 {frame_id} 帧")
                
                # 执行原始检测（不包含跟踪）- 只调用检测方法
                start_time = time.time()
                raw_detections, roi_area = service_instance._perform_raw_detection(frame)
                process_time = (time.time() - start_time) * 1000
                
                if task_count % 20 == 0:
                    print(f"[PDM] 工作线程 {worker_id} 完成第 {frame_id} 帧处理，耗时 {process_time:.2f}ms，检测到 {len(raw_detections) if raw_detections else 0} 个对象")
                
                # 将结果放入结果队列
                result_data = {
                    'frame_id': frame_id,
                    'raw_detections': raw_detections,
                    'roi_area': roi_area,
                    'frame': frame,
                    'process_time': process_time,
                    'worker_id': worker_id
                }
                
                try:
                    self.result_queue.put(result_data) # 使用阻塞模式
                    task_count += 1
                except queue.Full: # 理论上阻塞模式不应到此
                    print(f"[PDM] 警告: 工作线程 {worker_id}：结果队列已满，丢弃结果")
                
                self.frame_queue.task_done()
            except queue.Empty:
                # 偶尔输出等待日志，以减少日志量
                if task_count % 50 == 0:
                    print(f"[PDM] 工作线程 {worker_id} 等待新任务...")
                continue
            except Exception as e:
                print(f"[PDM] 工作线程 {worker_id} 错误: {e}")
                traceback.print_exc()
                break
        
        print(f"[PDM] 工作线程 {worker_id} 结束")
    
    def _result_processor_loop(self):
        """结果处理循环"""
        print("[PDM] 结果处理线程启动")
        result_count = 0
        # 优化：增加缓冲区清理阈值，减少不必要的清理操作
        BUFFER_CLEANUP_THRESHOLD = 60 

        while self.running:
            try:
                # 从结果队列获取数据，设置超时
                result_data = self.result_queue.get(timeout=1.0)
                
                if result_data is None:
                    continue

                # 解包工作线程传来的数据
                frame_id = result_data['frame_id']
                frame = result_data['frame']
                raw_detections = result_data['raw_detections']
                roi_area = result_data['roi_area']
                process_time = result_data['process_time']

                # 将结果存入缓冲区，以确保按顺序处理
                self.result_buffer[frame_id] = (frame, raw_detections, roi_area, process_time)

                # --- 关键修复：增加缓冲区清理逻辑，防止因丢帧导致死锁 ---
                if len(self.result_buffer) > BUFFER_CLEANUP_THRESHOLD:
                    # 找到当前缓冲区中最小的帧ID
                    min_buffered_id = min(self.result_buffer.keys())
                    # 如果最小的ID大于我们正在等待的ID，说明我们等待的帧可能已经丢失
                    if min_buffered_id > self.next_expected_frame_id:
                        print(f"[PDM] 警告: 等待的帧 {self.next_expected_frame_id} 可能已丢失。将从 {min_buffered_id} 继续。")
                        
                        # 清理掉所有比新起点旧的帧（虽然理论上不应该有）
                        keys_to_remove = [k for k in self.result_buffer if k < min_buffered_id]
                        for k in keys_to_remove:
                            del self.result_buffer[k]
                        
                        # 更新期望的帧ID，解开死锁
                        self.next_expected_frame_id = min_buffered_id

                # 按顺序处理缓冲区中的帧
                while self.next_expected_frame_id in self.result_buffer:
                    # 获取当前期望处理的帧数据
                    current_frame, current_detections, current_roi_area, current_process_time = self.result_buffer.pop(self.next_expected_frame_id)
                    
                    # --- 优化：将频繁的DEBUG日志设为条件输出 ---
                    if DEBUG:
                        print(f"[PDM-DEBUG] Frame {self.next_expected_frame_id}: 调用 global_tracker.update。")

                    # 更新全局跟踪器
                    filtered_detections = self.global_tracker.update(current_detections, current_roi_area)
                    
                    if DEBUG:
                        print(f"[PDM-DEBUG] Frame {self.next_expected_frame_id}: global_tracker.update 调用完成。")

                    # --- 优化：简化逻辑，直接使用跟踪器结果，移除后备机制 ---
                    objects_info = self._extract_objects_info(filtered_detections)
                    
                    # 更新处理时间和FPS
                    self.process_time = current_process_time
                    self._update_fps()
                    
                    # 更新最新结果以供网络广播
                    self._update_latest_results(objects_info, time.time())
                    
                    # 在控制台打印检测结果
                    self._print_detection_results(objects_info)
                    
                    # 准备用于显示的帧
                    if self.show_window:
                        self._prepare_display_data(current_frame, filtered_detections)
                    
                    # 增加计数器，指向下一帧
                    self.next_expected_frame_id += 1
                    result_count += 1
                    
                    # --- 优化：降低日志频率 ---
                    if result_count % 100 == 0:
                        print(f"[PDM] 结果处理线程已处理 {result_count} 帧")

            except queue.Empty:
                # 队列为空是正常情况，继续等待
                continue
            except Exception as e:
                # 捕获所有其他异常，打印后退出循环，防止线程崩溃
                print(f"[PDM] 结果处理线程发生致命错误: {e}")
                traceback.print_exc()
                break
        
        print("[PDM] 结果处理线程已停止")
    
    def _prepare_display_data(self, frame, filtered_detections):
        """准备显示数据（在结果处理线程中调用）"""
        try:
            # 检查帧是否有效
            if frame is None or frame.size == 0:
                print("[PDM] 警告: _prepare_display_data 收到空帧")
                return
                
            # 绘制ROI边界
            display_frame = frame.copy()
            
            if self.roi_points is not None and len(self.roi_points) > 0:
                # 绘制ROI边界
                roi_pts_np = np.array(self.roi_points, np.int32).reshape((-1, 1, 2))
                cv2.polylines(display_frame, [roi_pts_np], True, (0, 255, 0), 2)

                # 绘制ROI角点和归一化坐标系示意
                if len(self.roi_points) == 4:
                    colors = [(0, 0, 255), (255, 0, 0), (255, 255, 0), (0, 255, 255)]  # 红、蓝、黄、青
                    labels = ["(0,0)", "(1,0)", "(1,1)", "(0,1)"]
                    
                    for i, (point, color, label) in enumerate(zip(self.roi_points, colors, labels)):
                        cv2.circle(display_frame, tuple(point.astype(int)), 8, color, -1)
                        cv2.putText(display_frame, label, 
                                   (point[0] + 10, point[1] - 10), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            
            # 使用通用标注核心逻辑
            annotated_frame = DetectionServiceBase._static_annotate_objects_on_frame(
                frame_to_annotate=display_frame,
                detections_to_annotate=filtered_detections,
                box_annotator_instance=self.bounding_box_annotator,
                label_annotator_instance=self.label_annotator,
                class_names_map=CLASS_NAMES,
                precomputed_perspective_matrix_for_label=self.perspective_transform_matrix,
                draw_center_points=True,
                center_point_color=(0, 255, 0)
            )
            
            # 添加性能指标
            cv2.putText(annotated_frame, f"FPS: {self.fps:.1f}", (10, 30), 
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(annotated_frame, f"Delay: {self.process_time:.1f} ms", (10, 70), 
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(annotated_frame, f"Workers: {self.num_instances}", (10, 110), 
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            # 调整显示尺寸
            try:
                scale_factor = float(self.display_scale)
                if scale_factor <= 0:
                    scale_factor = 1.0
            except (ValueError, TypeError):
                scale_factor = 1.0
            
            # 获取原始图像尺寸并保持长宽比
            orig_height, orig_width = annotated_frame.shape[:2]
            
            # 计算目标尺寸，保持长宽比
            target_width = max(1, int(orig_width / scale_factor))
            target_height = max(1, int(orig_height / scale_factor))
            
            # 使用前检查是否有合法尺寸
            if target_width > 0 and target_height > 0 and orig_width > 0 and orig_height > 0:
                final_display_image = cv2.resize(annotated_frame, (target_width, target_height), interpolation=cv2.INTER_LINEAR)
                
                # 更新共享显示数据
                with self.display_lock:
                    self.display_frame = final_display_image.copy()
                    self.annotation_ready = True
                    
                    # 添加一个简单的标记告诉我们更新了显示数据
                    if not hasattr(self, 'display_update_counter'):
                        self.display_update_counter = 0
                    
                    self.display_update_counter += 1
                    if self.display_update_counter % 20 == 0:
                        print(f"[PDM] 显示帧已更新 ({self.display_update_counter})")
            else:
                print(f"[PDM] 警告: 调整显示尺寸时发现无效尺寸: orig=({orig_width},{orig_height}) target=({target_width},{target_height})")
        except Exception as e:
            print(f"[PDM] _prepare_display_data 错误: {e}")
            traceback.print_exc()

    def _extract_objects_info(self, filtered_detections):
        """从检测结果中提取对象信息（使用静态方法并传入PDM的预计算透视变换矩阵）"""
        return DetectionServiceBase._static_extract_objects_info(
            filtered_detections,
            CLASS_NAMES,
            self.roi_points.tolist(),
            self.perspective_transform_matrix
        )

    def _update_fps(self):
        """更新FPS"""
        self.frame_count += 1
        elapsed_time = time.time() - self.start_time
        if (elapsed_time >= 1.0):
            self.fps = self.frame_count / elapsed_time
            self.frame_count = 0
            self.start_time = time.time()
    
    def _update_latest_results(self, objects_info, frame_time):
        """更新最新结果并推送给订阅客户端"""
        with self.results_lock:
            self.latest_results = {
                "timestamp": frame_time,
                "fps": self.fps,
                "process_time_ms": self.process_time,
                "objects": objects_info,
                "parallel_info": {
                    "num_instances": self.num_instances,
                    "frame_queue_size": self.frame_queue.qsize(),
                    "result_queue_size": self.result_queue.qsize()
                }
            }
            
            # 推送结果给所有订阅客户端
            self.client_manager.broadcast_results(self.latest_results)
    
    def _print_detection_results(self, objects_info):
        """打印检测结果，使用新的键名和归一化坐标"""
        if not objects_info:
            # 每隔一段时间打印一次，避免刷屏
            if not hasattr(self, '_last_print_no_object_time') or time.time() - self._last_print_no_object_time > 5:
                print("当前帧未检测到物体")
                self._last_print_no_object_time = time.time()
            return
        
        print(f"\n当前检测到 {len(objects_info)} 个物体 (FPS: {self.fps:.1f}, Delay: {self.process_time:.1f}ms):")
        for obj in objects_info:
            track_number = obj.get("track_number", "无")
            class_name = obj.get("class_name", "N/A")
            center_abs = obj.get("center_abs_pixel", [0, 0])
            norm_center_coords = obj.get("center_normalized_prop", [None, None])
            norm_bbox_coords = obj.get("bounding_box_xyxy_normalized_prop", [None, None, None, None])
            confidence = obj.get("confidence", 0.0)
            
            norm_center_str = "N/A"
            if norm_center_coords and norm_center_coords[0] is not None and norm_center_coords[1] is not None:
                norm_center_str = f"({norm_center_coords[0]:.2f}, {norm_center_coords[1]:.2f})"
            
            norm_bbox_str = "N/A"
            if norm_bbox_coords and all(c is not None for c in norm_bbox_coords):
                norm_bbox_str = f"({norm_bbox_coords[0]:.2f}, {norm_bbox_coords[1]:.2f}, {norm_bbox_coords[2]:.2f}, {norm_bbox_coords[3]:.2f})"
            
            print(f"  编号: #{track_number}, 类别: {class_name}, "
                  f"中心(绝对): ({center_abs[0]:.1f}, {center_abs[1]:.1f}), "
                  f"中心(归一化): {norm_center_str}, "
                  f"边界框(归一化): {norm_bbox_str}, "
                  f"置信度: {confidence:.2f}")

    def _start_server(self):
        """启动通信服务器（支持客户端订阅）"""
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.server_socket.bind((NETWORK_HOST, self.port))
        self.server_socket.listen(5)
        self.server_socket.settimeout(0.1)
        
        print(f"并行检测服务器已启动，监听地址: {NETWORK_HOST}:{self.port}")
        
        while self.running:
            try:
                client_socket, addr = self.server_socket.accept()
                # 为每个客户端启动一个处理线程
                client_thread = threading.Thread(
                    target=self._handle_client,
                    args=(client_socket, addr)
                )
                client_thread.daemon = True
                client_thread.start()
            except socket.timeout:
                pass
            except Exception as e:
                if self.running:
                    print(f"服务器循环错误: {e}")
                break
        
        if self.server_socket:
            self.server_socket.close()
            print("并行检测服务器已关闭")
    
    def _handle_client(self, client_socket, addr):
        """处理单个客户端连接，响应订阅请求并保持连接以供数据推送"""
        client_id = None
        print(f"[PDM] 接受来自 {addr} 的新连接。")
        try:
            client_socket.settimeout(10.0) # 设置一个合理的超时以接收初始请求

            # 1. 接收客户端的初始消息
            initial_data_bytes = client_socket.recv(1024)
            if not initial_data_bytes:
                print(f"[PDM] 客户端 {addr} 连接后未发送数据，关闭连接。")
                client_socket.close()
                return

            initial_data_str = initial_data_bytes.decode('utf-8').strip()
            print(f"[PDM] 收到来自 {addr} 的初始数据: {initial_data_str}")

            # 2. 解析JSON并检查是否为订阅命令
            is_subscribe_request = False
            try:
                request_json = json.loads(initial_data_str)
                if isinstance(request_json, dict) and request_json.get('command') == 'subscribe':
                    is_subscribe_request = True
                    print(f"[PDM] 来自 {addr} 的请求确认为订阅命令。")
                else:
                    print(f"[PDM] 来自 {addr} 的请求不是有效的订阅命令: {initial_data_str}")
            except json.JSONDecodeError:
                print(f"[PDM] 来自 {addr} 的初始数据不是有效的JSON: {initial_data_str}")

            if is_subscribe_request:
                # 3. 注册客户端
                client_id = self.client_manager.add_client(client_socket, addr)

                # 4. 构造并发送JSON确认响应
                response_data = {"status": "subscribed", "message": f"Subscription successful for client {client_id}"}
                response_json_str = json.dumps(response_data) + '\n' # 确保有换行符
                client_socket.sendall(response_json_str.encode('utf-8'))
                print(f"[PDM] 已向客户端 {client_id} ({addr}) 发送订阅确认。")

                # 5. 保持连接，允许 ClientManager 推送数据，并检测客户端断开
                client_socket.settimeout(1.0) # 使用较短超时进行非阻塞检查
                while self.running:
                    try:
                        # 尝试接收少量数据以检测连接是否仍然存活或客户端是否发送了额外信息
                        # 对于纯推送模型，这里主要用于检测断开
                        ping_data = client_socket.recv(64) 
                        if not ping_data:
                            print(f"[PDM] 客户端 {client_id} ({addr}) 主动断开连接。")
                            break
                        # 如果你的客户端设计为还会发送其他命令到这个线程，可以在这里处理
                        # print(f"[PDM] 从客户端 {client_id} ({addr}) 收到额外数据: {ping_data.decode('utf-8')}")
                    except socket.timeout:
                        # 超时是正常的，继续检查 self.running 状态
                        continue
                    except ConnectionResetError:
                        print(f"[PDM] 客户端 {client_id} ({addr}) 连接被重置。")
                        break
                    except Exception as e:
                        print(f"[PDM] 与客户端 {client_id} ({addr}) 通信发生错误: {e}")
                        traceback.print_exc()
                        break
            else:
                # 如果不是有效的订阅请求，发送错误信息并关闭连接
                print(f"[PDM] 来自 {addr} 的请求未被处理为订阅，发送错误并关闭连接。")
                error_response = {"status": "error", "message": "Invalid or non-subscribe request"}
                error_json_str = json.dumps(error_response) + '\n'
                try:
                    client_socket.sendall(error_json_str.encode('utf-8'))
                except Exception as e_send_err:
                    print(f"[PDM] 发送错误响应给 {addr} 失败: {e_send_err}")
                finally:
                    client_socket.close()
        
        except socket.timeout:
            print(f"[PDM] 客户端 {addr} 初始请求超时。")
            if not (client_socket._closed if hasattr(client_socket, '_closed') else False):
                client_socket.close()
        except ConnectionResetError:
            print(f"[PDM] 客户端 {addr} 在初始通信时连接被重置。")
            # 连接已重置，套接字通常已无效，无需显式关闭，但确保 client_id 被处理
        except json.JSONDecodeError as e: # 如果初始数据解析失败，也可能在这里捕获
            print(f"[PDM] 处理来自 {addr} 的初始数据时JSON解析失败: {e}")
            if not (client_socket._closed if hasattr(client_socket, '_closed') else False):
                client_socket.close()
        except Exception as e:
            print(f"[PDM] 处理客户端 {addr} 时发生未预期错误: {e}")
            traceback.print_exc()
            if not (client_socket._closed if hasattr(client_socket, '_closed') else False) and client_id is None:
                # 如果发生未知错误且客户端未注册，尝试关闭socket
                try:
                    client_socket.close()
                except Exception:
                    pass
        finally:
            if client_id is not None: # 只有成功订阅并注册的客户端才从管理器移除
                print(f"[PDM] 从管理器移除客户端 {client_id} ({addr})。")
                self.client_manager.remove_client(client_id)
            # 对于其他情况，如果socket仍然打开且未注册，确保它被关闭
            elif not (client_socket._closed if hasattr(client_socket, '_closed') else False):
                 try:
                    client_socket.close()
                    print(f"[PDM] 客户端 {addr} 未注册，套接字已关闭。")
                 except Exception:
                    pass # 忽略关闭错误

    def start(self):
        """启动并行检测管理器"""
        print("[PDM] 开始启动并行检测管理器")
        
        if not self._init_camera():
            print("[PDM] 摄像头初始化失败，无法启动")
            return
        
        self.running = True
        
        # 如果显示窗口，先在主线程创建窗口，使用 WINDOW_NORMAL 模式
        if self.show_window:
            cv2.namedWindow(self.window_title, cv2.WINDOW_NORMAL)
            # 尝试设置初始窗口尺寸，确保窗口可见
            cv2.resizeWindow(self.window_title, 800, 600)
            print("[PDM] 已创建显示窗口")
        
        # 创建工作实例
        self._create_worker_instances()
        
        # 初始化一个空白帧，避免窗口一开始就是灰色的
        if self.show_window:
            blank_frame = np.zeros((480, 640, 3), dtype=np.uint8)
            cv2.putText(blank_frame, "waiting for data...", (200, 240), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            with self.display_lock:
                self.display_frame = blank_frame
            
            # 初始显示空白帧
            cv2.imshow(self.window_title, blank_frame)
            cv2.waitKey(1)
        
        # 启动帧读取线程
        self.frame_reader_thread = threading.Thread(target=self._frame_reader_loop)
        self.frame_reader_thread.start()
        
        # 4. 启动工作线程
        for i, instance in enumerate(self.worker_instances):
            thread = threading.Thread(target=self._worker_task, args=(i, instance))
            thread.start()
            self.worker_threads.append(thread)
            print(f"--- [DIAG] 已创建工作线程 {i}")
        
        print(f"[PDM] 已启动 {len(self.worker_threads)} 个工作线程。")

        # 5. 启动结果处理线程
        self.result_processor_thread = threading.Thread(target=self._result_processor_loop)
        self.result_processor_thread.start()
        
        # 启动网络服务线程
        self.server_thread = threading.Thread(target=self._start_server)
        self.server_thread.daemon = True
        self.server_thread.start()
        
        print(f"[PDM] 并行检测管理器已启动，使用 {self.num_instances} 个工作实例")
        
        try:
            # 主线程负责窗口显示和事件处理
            frame_display_count = 0
            while self.running:
                if self.show_window:
                    # 获取最新的显示帧
                    current_frame = None
                    with self.display_lock:
                        if self.display_frame is not None:
                            current_frame = self.display_frame.copy()
                    
                    # 如果有可显示的帧，则显示
                    if current_frame is not None:
                        # 每100帧输出一次日志
                        if frame_display_count % 100 == 0:
                            print(f"[PDM] 主线程显示第 {frame_display_count} 帧")
                        
                        try:
                            cv2.imshow(self.window_title, current_frame)
                            frame_display_count += 1
                        except Exception as e:
                            print(f"[PDM] 显示帧错误: {e}")
                            traceback.print_exc()
                    else:
                        if frame_display_count % 50 == 0:
                            print("[PDM] 主线程等待显示帧...")
                    
                    # 处理键盘事件
                    k = cv2.waitKey(1)
                    if k == 27:  # ESC键
                        print("[PDM] 用户按下ESC键，退出程序...")
                        self.running = False
                        break
                else:
                    time.sleep(0.1)
        except KeyboardInterrupt:
            print("[PDM] 接收到键盘中断，正在停止...")
        except Exception as e:
            print(f"[PDM] 主循环错误: {e}")
            traceback.print_exc()
        finally:
            self.stop()
    
    def stop(self):
        """停止并行检测管理器"""
        print("[PDM] 正在停止并行检测管理器...")
        self.running = False
        
        # 先关闭服务器套接字，让server_thread退出accept阻塞
        if hasattr(self, 'server_socket') and self.server_socket:
            try:
                self.server_socket.close()
                print("[PDM] 服务器套接字已关闭")
            except Exception as e:
                print(f"[PDM] 关闭服务器套接字出错: {e}")
        
        # 向队列发送停止信号
        for _ in range(self.num_instances):
            try:
                self.frame_queue.put((None, -1), timeout=1.0)
            except queue.Full:
                pass
        
        try:
            self.result_queue.put(None, timeout=1.0)
        except queue.Full:
            pass
        
        # 等待线程结束
        if self.frame_reader_thread:
            self.frame_reader_thread.join(timeout=3.0)
            print("[PDM] 帧读取线程已停止")
        
        for i, worker_thread in enumerate(self.worker_threads):
            worker_thread.join(timeout=3.0)
            print(f"[PDM] 工作线程 {i} 已停止")
        
        if self.result_processor_thread:
            self.result_processor_thread.join(timeout=3.0)
            print("[PDM] 结果处理线程已停止")
        
        # 等待server_thread (即使是daemon线程也最好显式join)
        if self.server_thread:
            self.server_thread.join(timeout=1.0)
        
        # 释放资源
        if self.cap:
            self.cap.release()
            print("[PDM] 摄像头已释放")
        
        if self.show_window:
            cv2.destroyAllWindows()
            print("[PDM] 窗口已关闭")
        
        print("[PDM] 并行检测管理器已停止")
        
        # 强制退出程序
        os._exit(0)  # 使用os._exit强制终止进程，比sys.exit更彻底

def main():
    """主函数，用于启动检测服务或并行管理器"""
    # --- 关键修复：强制所有输出使用UTF-8编码 ---
    # 这可以解决将输出重定向到文件时，在Windows上可能出现的乱码问题
    if sys.stdout.encoding != 'utf-8':
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    if sys.stderr.encoding != 'utf-8':
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
        
    print("[main] 程序启动...")
    
    parser = argparse.ArgumentParser(description="YOLO检测服务")
    parser.add_argument('--as-service', action='store_true', help='作为服务运行，不显示可视化窗口')
    parser.add_argument('--camera', type=int, help='摄像头ID，不指定则使用配置文件中的值')
    parser.add_argument('--port', type=int, help='服务器端口，不指定则使用配置文件中的值')
    args = parser.parse_args()
    
    print(f"[main] 解析参数: as-service={args.as_service}, camera={args.camera}, port={args.port}")
    
    # 从配置中读取并行处理设置，并确保至少有2个实例
    num_instances = config.get('performance', {}).get('num_instances', 2)
    if num_instances < 2:
        num_instances = 2
    print(f"[main] 启动并行检测模式，使用 {num_instances} 个工作实例")
    
    try:
        print(f"[main] 创建并行检测管理器实例...")
        parallel_manager = ParallelDetectionManager(
            num_instances=num_instances,
            show_window=not args.as_service,
            camera_id=args.camera,
            port=args.port
        )
        
        print(f"[main] 启动并行检测管理器...")
        parallel_manager.start()
    except Exception as e:
        print(f"[main] 运行过程中发生异常: {e}")
        traceback.print_exc()
    finally:
        print(f"[main] 清理并行检测管理器资源...")
        if 'parallel_manager' in locals() and hasattr(parallel_manager, 'running'):
            parallel_manager.stop()

if __name__ == "__main__":
    main()