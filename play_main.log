2025-07-15 22:40:49,214 - [MainThread] - INFO - 日志系统初始化完成
2025-07-15 22:40:49,214 - [MainThread] - INFO - 应用程序启动
2025-07-15 22:40:49,991 - [MainThread] - INFO - 信号处理器设置完成（仅置标志）
2025-07-15 22:40:49,991 - [MainThread] - INFO - 信号处理器已设置(使用共享状态字典)
2025-07-15 22:40:52,231 - [MainThread] - INFO - Web显示进程启动成功
2025-07-15 22:40:52,448 - [MainThread] - INFO - GUI 进程已通过 multiprocessing.Process 启动, PID: 16236
2025-07-15 22:40:52,448 - [MainThread] - INFO - 每个会话最大免费游戏次数: 10
2025-07-15 22:40:52,448 - [MainThread] - INFO - 初始化全局 OBSController
2025-07-15 22:40:52,448 - [MainThread] - INFO - 正在连接到OBS WebSocket (host=localhost, port=4455)...
2025-07-15 22:40:52,448 - [MainThread] - INFO - Connecting with parameters: host='localhost' port=4455 password='' subs=0 timeout=5
2025-07-15 22:40:56,488 - [MainThread] - ERROR - ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Program Files\Python39\lib\site-packages\obsws_python\baseclient.py", line 41, in __init__
    self.ws.connect(f"ws://{self.host}:{self.port}", timeout=self.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_core.py", line 256, in connect
    self.sock, addrs = connect(
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 145, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 232, in _open_socket
    raise err
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 209, in _open_socket
    sock.connect(address)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-15 22:40:56,488 - [MainThread] - ERROR - 连接到OBS WebSocket失败: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-15 22:40:56,495 - [MainThread] - INFO - 检查历史会话记录...
2025-07-15 22:40:56,495 - [MainThread] - INFO - 未找到历史会话记录，将创建新会话
2025-07-15 22:40:56,511 - [MainThread] - INFO - 已创建新会话，ID: 1
2025-07-15 22:40:56,511 - [MainThread] - INFO - 当前使用会话 ID: 1
2025-07-15 22:40:56,511 - [MainThread] - DEBUG - 当前会话ID已设置为: 1
2025-07-15 22:40:56,526 - [MainThread] - INFO - 会话开始时间: None
2025-07-15 22:40:56,526 - [MainThread] - INFO - 使用新创建的会话，初始化空数据结构
2025-07-15 22:40:56,526 - [MainThread] - INFO - 初始化全局数据库同步管理器
2025-07-15 22:40:56,526 - [MainThread] - INFO - 启动数据库同步线程，同步间隔: 10秒
2025-07-15 22:40:56,526 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-15 22:40:56,526 - [MainThread] - INFO - 会话初始化完成，新会话，会话ID: 1
2025-07-15 22:40:56,526 - [DBSyncThread] - DEBUG - [全量同步] player_info 为空，跳过 comments_after_game 同步。
2025-07-15 22:40:56,542 - [MainThread] - INFO - 成功连接到移动服务 localhost:5556
2025-07-15 22:40:56,542 - [MoveServiceEventReceiver] - INFO - 移动服务事件接收线程已启动。
2025-07-15 22:40:56,542 - [MainThread] - INFO - 移动服务客户端已启动，连接到: localhost:5556
2025-07-15 22:40:56,542 - [DetectionReceiver] - INFO - 检测数据接收器启动
2025-07-15 22:40:56,542 - [MainThread] - INFO - 检测数据接收器线程已启动
2025-07-15 22:40:56,542 - [MainThread] - INFO - [虚拟玩家] 成功加载 300 个虚拟玩家，起始索引: 294
2025-07-15 22:40:56,542 - [MainThread] - INFO - [虚拟玩家] 管理器启动...
2025-07-15 22:40:56,542 - [MainThread] - INFO - 虚拟玩家管理器已启动
2025-07-15 22:40:56,542 - [MainThread] - INFO - 状态更新处理线程已启动
2025-07-15 22:40:56,542 - [MainThread] - INFO - GUI的FPS发送线程已启动
2025-07-15 22:40:56,542 - [DetectionReceiver] - INFO - 成功连接到检测服务器 localhost:5555
2025-07-15 22:40:56,542 - [MainThread] - INFO - 游戏处理线程已启动
2025-07-15 22:40:56,542 - [DetectionReceiver] - DEBUG - 已发送订阅命令
2025-07-15 22:40:56,542 - [StatusUpdateThread] - INFO - [重试机制] 检测到移动服务端已恢复，若有pending_retry_player将立即重试。
2025-07-15 22:40:56,542 - [MainThread] - INFO - 主循环监控线程已启动
2025-07-15 22:40:56,557 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-15 22:40:56,573 - [MainThread] - INFO - [消息线程] 启动消息获取后台线程
2025-07-15 22:40:56,573 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-15 22:40:56,573 - [MessagePollThread] - DEBUG - [消息线程] 开始运行消息获取线程
2025-07-15 22:40:56,573 - [MainThread] - INFO - [消息线程] 消息获取线程已启动
2025-07-15 22:40:56,573 - [DetectionReceiver] - INFO - 收到订阅确认: Subscription successful for client 10
2025-07-15 22:40:56,573 - [MainThread] - INFO - 消息获取后台线程已启动，开始接收消息...
2025-07-15 22:40:56,573 - [MessagePollThread] - DEBUG - [消息流] 启动健康检查
2025-07-15 22:40:56,573 - [MainThread] - INFO - 开始处理消息，按Ctrl+C停止...
2025-07-15 22:40:56,573 - [HealthCheckThread] - DEBUG - [健康检查] 任务开始
2025-07-15 22:40:56,573 - [MessagePollThread] - INFO - 健康检查线程已启动，间隔: 30秒
2025-07-15 22:40:56,573 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-15 22:40:56,573 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:40:56,573 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 7.6 秒
2025-07-15 22:40:56,573 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:40:56,573 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-15 22:40:56,595 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:40:56,595 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:40:56,595 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 1
2025-07-15 22:40:56,595 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:40:56,595 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:40:56,611 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:56,626 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:40:56,626 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:40:56,626 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:40:56,626 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590456.626874
2025-07-15 22:40:56,626 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:40:56,626 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:40:56,626 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:40:56,626 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:40:56,642 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:40:56,642 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:40:56,642 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=0.0s, 心跳间隔=1s
2025-07-15 22:40:56,642 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:40:56,658 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:56,706 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:56,744 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:56,786 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:56,826 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:56,876 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:56,911 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:56,953 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,008 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,046 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,095 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-15 22:40:57,095 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,095 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-15 22:40:57,134 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,180 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,229 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,276 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,311 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,355 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,391 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,439 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,478 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,514 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,561 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,600 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,608 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-15 22:40:57,608 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-15 22:40:57,640 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,661 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:40:57,663 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:40:57,663 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:40:57,663 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:40:57,663 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:40:57,663 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:40:57,663 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 2
2025-07-15 22:40:57,663 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:40:57,663 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:40:57,679 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,679 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:40:57,696 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:40:57,696 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:40:57,696 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590457.6965477
2025-07-15 22:40:57,696 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:40:57,699 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:40:57,699 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:40:57,699 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:40:57,700 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:40:57,700 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:40:57,700 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:40:57,700 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:40:57,700 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:40:57,700 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:40:57,717 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,756 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,796 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,835 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,876 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,912 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:57,959 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,012 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,050 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,091 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,128 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-15 22:40:58,128 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,128 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-15 22:40:58,166 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,212 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,258 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,300 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,341 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,387 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,434 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,471 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,525 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,564 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(0) < 目标(6)，添加虚拟玩家。
2025-07-15 22:40:58,564 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,564 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['忘记过往(0次)', '子葵(0次)', '仙鹤(0次)', '小邪恶(0次)', '醉花醉月(0次)']。已选择: 忘记过往
2025-07-15 22:40:58,584 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 忘记过往 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 22:40:58,644 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {}, pending_retry_player: None
2025-07-15 22:40:58,646 - [GameProcessThread] - INFO - [游戏线程] 玩家 忘记过往(VirtualPlayerID0295) 开始免费游戏，扣除免费次数 (1)
2025-07-15 22:40:58,648 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 忘记过往(VirtualPlayerID0295) 确保抓中, z_offset_extra=0
2025-07-15 22:40:58,653 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=3849d6e0-1265-4749-94b7-e2636c6d3f3c, Player=忘记过往, Target=4, Z_Offset_Extra=0.0
2025-07-15 22:40:58,655 - [GameProcessThread] - INFO - [游戏线程] 玩家 忘记过往(VirtualPlayerID0295) 抓取指令已发送到移动服务，命令ID: 3849d6e0-1265-4749-94b7-e2636c6d3f3c
2025-07-15 22:40:58,656 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,672 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c', 'status': 'queued', 'message': '抓取物体 4 指令已加入队列'}
2025-07-15 22:40:58,672 - [MoveServiceEventReceiver] - INFO - 抓取指令 3849d6e0-1265-4749-94b7-e2636c6d3f3c 已被移动服务接受并加入队列
2025-07-15 22:40:58,697 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,712 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:40:58,712 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:40:58,712 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:40:58,712 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:40:58,712 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:40:58,712 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:40:58,712 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 3
2025-07-15 22:40:58,712 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:40:58,712 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:40:58,728 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,790 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:40:58,791 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,793 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:40:58,794 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:40:58,795 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590458.795418
2025-07-15 22:40:58,795 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:40:58,796 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:40:58,796 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:40:58,797 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:40:58,798 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:40:58,799 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:40:58,800 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 22:40:58,800 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:40:58,801 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:40:58,801 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:40:58,816 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,864 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,909 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,947 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:58,993 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,030 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,066 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,098 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0295 订单状态: {'free_games_used_this_session': 1}
2025-07-15 22:40:59,109 - [DBSyncThread] - WARNING - 尝试更新玩家 VirtualPlayerID0295 订单状态，但该玩家在会话 1 中不存在
2025-07-15 22:40:59,115 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,154 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,167 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {'player_id': 'VirtualPlayerID0295', 'name': '忘记过往', 'start_time': 1752590458.646953, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c'}, pending_retry_player: None
2025-07-15 22:40:59,168 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-15 22:40:59,200 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,236 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,282 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,324 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,373 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,409 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,447 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,496 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,542 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,590 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,632 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,679 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {'player_id': 'VirtualPlayerID0295', 'name': '忘记过往', 'start_time': 1752590458.646953, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c'}, pending_retry_player: None
2025-07-15 22:40:59,679 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,680 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-15 22:40:59,723 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,771 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,815 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,815 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:40:59,815 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:40:59,815 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:40:59,831 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:40:59,834 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:40:59,834 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:40:59,835 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 4
2025-07-15 22:40:59,835 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:40:59,835 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:40:59,840 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:40:59,842 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:40:59,843 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:40:59,844 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590459.8447716
2025-07-15 22:40:59,844 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:40:59,845 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:40:59,845 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:40:59,846 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:40:59,860 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:40:59,862 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,862 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:40:59,863 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:40:59,865 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:40:59,865 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:40:59,865 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:40:59,915 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:40:59,959 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:41:00,011 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:41:00,049 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:41:00,091 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:41:00,141 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:41:00,188 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:41:00,188 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {'player_id': 'VirtualPlayerID0295', 'name': '忘记过往', 'start_time': 1752590458.646953, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c'}, pending_retry_player: None
2025-07-15 22:41:00,188 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-15 22:41:00,228 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:41:00,275 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:41:00,313 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:41:00,350 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:41:00,392 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:41:00,437 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:41:00,472 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:41:00,514 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:41:00,556 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:41:00,598 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 忘记过往 已完成游戏，总游戏次数: 1
2025-07-15 22:41:00,600 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 22:41:00,600 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(0) < 目标(5)，添加虚拟玩家。
2025-07-15 22:41:00,600 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['子葵(0次)', '仙鹤(0次)', '小邪恶(0次)', '醉花醉月(0次)', '忘记过往(1次)']。已选择: 子葵
2025-07-15 22:41:00,600 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 子葵 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 22:41:00,701 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0295', 'name': '忘记过往', 'start_time': 1752590458.646953, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c'}, pending_retry_player: None
2025-07-15 22:41:00,701 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:00,876 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:00,876 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:00,876 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:00,876 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:00,876 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:00,876 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:00,876 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 5
2025-07-15 22:41:00,876 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:00,876 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:00,893 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:00,901 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:00,917 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:00,923 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590460.923459
2025-07-15 22:41:00,924 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:00,924 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:00,924 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:00,924 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:00,924 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:00,924 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:00,924 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 22:41:00,924 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:00,924 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:00,924 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:01,212 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0295', 'name': '忘记过往', 'start_time': 1752590458.646953, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c'}, pending_retry_player: None
2025-07-15 22:41:01,212 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:01,415 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '4', 'message': '物品_4'}}
2025-07-15 22:41:01,415 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=3849d6e0-1265-4749-94b7-e2636c6d3f3c
2025-07-15 22:41:01,428 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '4', 'message': '物品_4'}
2025-07-15 22:41:01,428 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0295, player_name=忘记过往, requested_object_id=4
2025-07-15 22:41:01,428 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 4, 物品名称: '物品_4', 原始消息: '物品_4'
2025-07-15 22:41:01,429 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c', 'player_id': 'VirtualPlayerID0295', 'player_name': '忘记过往', 'item_name': '物品_4', 'success': True, 'object_id': '4', 'message': '物品_4', 'source': 'real_mode'}
2025-07-15 22:41:01,429 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 忘记过往(VirtualPlayerID0295), 成功: True, 物品: 物品_4, 物品ID: 4, 来源: real_mode, ReqID: 3849d6e0-1265-4749-94b7-e2636c6d3f3c, 消息: 物品_4
2025-07-15 22:41:01,429 - [StatusUpdateThread] - INFO - 玩家 忘记过往 本次是第 1 次成功抓取。
2025-07-15 22:41:01,429 - [StatusUpdateThread] - INFO - 为玩家 忘记过往 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-15 22:41:01,429 - [StatusUpdateThread] - INFO - 显示祝贺特效视频：玩家=忘记过往, 物品=物品_4
2025-07-15 22:41:01,429 - [StatusUpdateThread] - INFO - 正在连接到OBS WebSocket (host=localhost, port=4455)...
2025-07-15 22:41:01,429 - [StatusUpdateThread] - INFO - Connecting with parameters: host='localhost' port=4455 password='' subs=0 timeout=5
2025-07-15 22:41:01,733 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0295', 'name': '忘记过往', 'start_time': 1752590458.646953, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c'}, pending_retry_player: None
2025-07-15 22:41:01,733 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:01,934 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:01,934 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:01,934 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:01,934 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:01,934 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:01,934 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:01,934 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 6
2025-07-15 22:41:01,934 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:01,934 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:01,951 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:01,957 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:01,958 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:01,958 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590461.958257
2025-07-15 22:41:01,958 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:01,958 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:01,958 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:01,958 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:01,958 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:01,958 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:01,958 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:01,958 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:01,958 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:01,966 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:02,252 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0295', 'name': '忘记过往', 'start_time': 1752590458.646953, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c'}, pending_retry_player: None
2025-07-15 22:41:02,252 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:02,616 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(1) < 目标(3)，添加虚拟玩家。
2025-07-15 22:41:02,621 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['仙鹤(0次)', '小邪恶(0次)', '醉花醉月(0次)', '忘记过往(1次)']。已选择: 仙鹤
2025-07-15 22:41:02,624 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 仙鹤 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 22:41:02,768 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0295', 'name': '忘记过往', 'start_time': 1752590458.646953, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c'}, pending_retry_player: None
2025-07-15 22:41:02,768 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:02,981 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:02,981 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:02,981 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:02,981 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:02,981 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:02,981 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:02,981 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 7
2025-07-15 22:41:02,981 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:02,981 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:02,997 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:02,997 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:03,005 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:03,005 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590463.0051122
2025-07-15 22:41:03,005 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:03,005 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:03,005 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:03,005 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:03,005 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:03,005 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:03,005 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 22:41:03,005 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:03,005 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:03,005 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:03,290 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0295', 'name': '忘记过往', 'start_time': 1752590458.646953, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c'}, pending_retry_player: None
2025-07-15 22:41:03,290 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:03,798 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0295', 'name': '忘记过往', 'start_time': 1752590458.646953, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c'}, pending_retry_player: None
2025-07-15 22:41:03,799 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:04,032 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:04,034 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:04,034 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:04,035 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:04,035 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:04,036 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:04,037 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 8
2025-07-15 22:41:04,037 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:04,038 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:04,042 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:04,043 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:04,043 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:04,043 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590464.0439284
2025-07-15 22:41:04,043 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:04,043 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:04,043 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:04,043 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:04,043 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:04,058 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:04,059 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:04,061 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:04,062 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:04,064 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:04,307 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0295', 'name': '忘记过往', 'start_time': 1752590458.646953, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c'}, pending_retry_player: None
2025-07-15 22:41:04,307 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:04,638 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(2) < 目标(7)，添加虚拟玩家。
2025-07-15 22:41:04,638 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['小邪恶(0次)', '醉花醉月(0次)', '忘记过往(1次)']。已选择: 小邪恶
2025-07-15 22:41:04,641 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小邪恶 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 22:41:04,811 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0295', 'name': '忘记过往', 'start_time': 1752590458.646953, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c'}, pending_retry_player: None
2025-07-15 22:41:04,811 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:05,066 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:05,066 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:05,066 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:05,066 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:05,066 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:05,066 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:05,066 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 9
2025-07-15 22:41:05,066 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:05,082 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:05,097 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:05,112 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:05,113 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:05,115 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590465.1154296
2025-07-15 22:41:05,115 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:05,115 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:05,116 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:05,116 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:05,117 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:05,117 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:05,118 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 22:41:05,118 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:05,119 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:05,119 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:05,318 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0295', 'name': '忘记过往', 'start_time': 1752590458.646953, 'target_id': '4', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c'}, pending_retry_player: None
2025-07-15 22:41:05,318 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:05,496 - [StatusUpdateThread] - ERROR - ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Program Files\Python39\lib\site-packages\obsws_python\baseclient.py", line 41, in __init__
    self.ws.connect(f"ws://{self.host}:{self.port}", timeout=self.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_core.py", line 256, in connect
    self.sock, addrs = connect(
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 145, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 232, in _open_socket
    raise err
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 209, in _open_socket
    sock.connect(address)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-15 22:41:05,496 - [StatusUpdateThread] - ERROR - 连接到OBS WebSocket失败: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-15 22:41:05,515 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '3849d6e0-1265-4749-94b7-e2636c6d3f3c', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-15 22:41:05,518 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=3849d6e0-1265-4749-94b7-e2636c6d3f3c
2025-07-15 22:41:05,521 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-15 22:41:05,523 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0295, player_name=忘记过往, requested_object_id=4
2025-07-15 22:41:05,529 - [MoveServiceEventReceiver] - INFO - 移动服务请求 3849d6e0-1265-4749-94b7-e2636c6d3f3c (cycle_completed) 已完成，从活动命令中移除。
2025-07-15 22:41:05,704 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 忘记过往(VirtualPlayerID0295)
2025-07-15 22:41:05,705 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_4'，准备结束游戏。
2025-07-15 22:41:05,707 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 忘记过往(VirtualPlayerID0295), 结果: 物品_4, 订单: None
2025-07-15 22:41:05,708 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-15 22:41:05,708 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-15 22:41:05,749 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.035秒 - 玩家: VirtualPlayerID0295, 结果: 物品_4, 订单: NULL
2025-07-15 22:41:05,749 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0295] = 1
2025-07-15 22:41:05,846 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {}, pending_retry_player: None
2025-07-15 22:41:05,853 - [GameProcessThread] - INFO - [游戏线程] 玩家 子葵(VirtualPlayerID0296) 开始免费游戏，扣除免费次数 (1)
2025-07-15 22:41:05,853 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 子葵(VirtualPlayerID0296) 确保抓中, z_offset_extra=0
2025-07-15 22:41:05,853 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=e1544ac3-cfae-47f7-9602-f36c9c5b71cf, Player=子葵, Target=2, Z_Offset_Extra=0.0
2025-07-15 22:41:05,853 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'e1544ac3-cfae-47f7-9602-f36c9c5b71cf', 'status': 'queued', 'message': '抓取物体 2 指令已加入队列'}
2025-07-15 22:41:05,853 - [GameProcessThread] - INFO - [游戏线程] 玩家 子葵(VirtualPlayerID0296) 抓取指令已发送到移动服务，命令ID: e1544ac3-cfae-47f7-9602-f36c9c5b71cf
2025-07-15 22:41:05,866 - [MoveServiceEventReceiver] - INFO - 抓取指令 e1544ac3-cfae-47f7-9602-f36c9c5b71cf 已被移动服务接受并加入队列
2025-07-15 22:41:06,125 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:06,125 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:06,140 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:06,140 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:06,140 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:06,140 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:06,140 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 10
2025-07-15 22:41:06,140 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:06,140 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:06,151 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:06,153 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:06,155 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:06,162 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590466.162252
2025-07-15 22:41:06,163 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:06,165 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:06,166 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:06,167 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:06,170 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:06,170 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:06,185 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:06,185 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:06,185 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:06,185 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:06,265 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0296 订单状态: {'free_games_used_this_session': 1}
2025-07-15 22:41:06,272 - [DBSyncThread] - WARNING - 尝试更新玩家 VirtualPlayerID0296 订单状态，但该玩家在会话 1 中不存在
2025-07-15 22:41:06,377 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0296', 'name': '子葵', 'start_time': 1752590465.8536513, 'target_id': '2', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'e1544ac3-cfae-47f7-9602-f36c9c5b71cf'}, pending_retry_player: None
2025-07-15 22:41:06,377 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:06,506 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'e1544ac3-cfae-47f7-9602-f36c9c5b71cf', 'event_name': 'hardware_error', 'data': {'error_message': '检测到电机硬件故障，已终止本轮操作', 'faults': [{'axis': 'motor_x', 'fault_type': 'hardware_fault', 'fault_msg': '模拟硬件故障（测试）'}, {'axis': 'motor_z', 'fault_type': 'timeout_fault', 'fault_msg': '模拟超时故障（测试）'}], 'player_id': 'VirtualPlayerID0296', 'player_name': '子葵'}}
2025-07-15 22:41:06,506 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='hardware_error', request_id=e1544ac3-cfae-47f7-9602-f36c9c5b71cf
2025-07-15 22:41:06,510 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'error_message': '检测到电机硬件故障，已终止本轮操作', 'faults': [{'axis': 'motor_x', 'fault_type': 'hardware_fault', 'fault_msg': '模拟硬件故障（测试）'}, {'axis': 'motor_z', 'fault_type': 'timeout_fault', 'fault_msg': '模拟超时故障（测试）'}], 'player_id': 'VirtualPlayerID0296', 'player_name': '子葵'}
2025-07-15 22:41:06,510 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0296, player_name=子葵, requested_object_id=2
2025-07-15 22:41:06,510 - [MoveServiceEventReceiver] - INFO - [事件转换] 处理其他运动模块事件: 'hardware_error'
2025-07-15 22:41:06,511 - [MoveServiceEventReceiver] - DEBUG - [事件发送] 通用移动服务事件 'hardware_error' 已发送: {'type': 'move_service_event', 'request_id': 'e1544ac3-cfae-47f7-9602-f36c9c5b71cf', 'player_id': 'VirtualPlayerID0296', 'player_name': '子葵', 'requested_object_id': '2', 'event_name': 'hardware_error', 'data': {'error_message': '检测到电机硬件故障，已终止本轮操作', 'faults': [{'axis': 'motor_x', 'fault_type': 'hardware_fault', 'fault_msg': '模拟硬件故障（测试）'}, {'axis': 'motor_z', 'fault_type': 'timeout_fault', 'fault_msg': '模拟超时故障（测试）'}], 'player_id': 'VirtualPlayerID0296', 'player_name': '子葵'}}
2025-07-15 22:41:06,512 - [StatusUpdateThread] - ERROR - [移动服务端故障] hardware_error for 子葵(VirtualPlayerID0296), request_id=e1544ac3-cfae-47f7-9602-f36c9c5b71cf, data={"error_message": "检测到电机硬件故障，已终止本轮操作", "faults": [{"axis": "motor_x", "fault_type": "hardware_fault", "fault_msg": "模拟硬件故障（测试）"}, {"axis": "motor_z", "fault_type": "timeout_fault", "fault_msg": "模拟超时故障（测试）"}], "player_id": "VirtualPlayerID0296", "player_name": "子葵"}
2025-07-15 22:41:06,512 - [StatusUpdateThread] - ERROR - [硬件故障详情] axis=motor_x, fault_type=hardware_fault, fault_msg=模拟硬件故障（测试）
2025-07-15 22:41:06,513 - [StatusUpdateThread] - ERROR - [硬件故障详情] axis=motor_z, fault_type=timeout_fault, fault_msg=模拟超时故障（测试）
2025-07-15 22:41:06,513 - [StatusUpdateThread] - WARNING - [重试机制] 玩家 子葵(VirtualPlayerID0296) 已进入pending_retry_player，等待移动服务端恢复后重试。
2025-07-15 22:41:06,658 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 子葵 已完成游戏，总游戏次数: 1
2025-07-15 22:41:06,658 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(2) < 目标(5)，添加虚拟玩家。
2025-07-15 22:41:06,658 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['醉花醉月(0次)', '忘记过往(1次)', '子葵(1次)']。已选择: 醉花醉月
2025-07-15 22:41:06,658 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 醉花醉月 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 22:41:06,890 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {}, pending_retry_player: {'entry_tuple': (['', 1752590465.8536513, '子葵', 'VirtualPlayerID0296', '2', '', '机器人模型'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0296', 'name': '子葵', 'start_time': 1752590465.8536513, 'target_id': '2', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'e1544ac3-cfae-47f7-9602-f36c9c5b71cf'}}
2025-07-15 22:41:06,890 - [GameProcessThread] - INFO - [重试机制] 检测到pending_retry_player，优先重试玩家: 子葵(VirtualPlayerID0296)
2025-07-15 22:41:06,890 - [GameProcessThread] - INFO - [游戏线程] 玩家 子葵(VirtualPlayerID0296) 开始免费游戏，扣除免费次数 (2)
2025-07-15 22:41:06,890 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 子葵(VirtualPlayerID0296) 确保抓中, z_offset_extra=0
2025-07-15 22:41:06,890 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=f46a5bad-2909-4bed-bc82-aff35999c962, Player=子葵, Target=2, Z_Offset_Extra=0.0
2025-07-15 22:41:06,897 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'f46a5bad-2909-4bed-bc82-aff35999c962', 'status': 'queued', 'message': '抓取物体 2 指令已加入队列'}
2025-07-15 22:41:06,897 - [GameProcessThread] - INFO - [游戏线程] 玩家 子葵(VirtualPlayerID0296) 抓取指令已发送到移动服务，命令ID: f46a5bad-2909-4bed-bc82-aff35999c962
2025-07-15 22:41:06,898 - [MoveServiceEventReceiver] - INFO - 抓取指令 f46a5bad-2909-4bed-bc82-aff35999c962 已被移动服务接受并加入队列
2025-07-15 22:41:06,987 - [DBSyncThread] - INFO - 数据库操作: 更新队列完成，耗时 0.034秒
2025-07-15 22:41:06,990 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-15 22:41:06,996 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 5 个玩家, 成功更新 5 条记录.
2025-07-15 22:41:07,197 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:07,205 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:07,208 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:07,208 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:07,208 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:07,209 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:07,209 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 11
2025-07-15 22:41:07,209 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:07,210 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:07,223 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:07,230 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:07,233 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:07,235 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590467.2354772
2025-07-15 22:41:07,235 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:07,236 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:07,237 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:07,238 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:07,241 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:07,241 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:07,241 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:07,242 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:07,245 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:07,246 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:07,406 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0296', 'name': '子葵', 'start_time': 1752590466.8901434, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f46a5bad-2909-4bed-bc82-aff35999c962'}, pending_retry_player: None
2025-07-15 22:41:07,411 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:07,511 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0296 订单状态: {'free_games_used_this_session': 2}
2025-07-15 22:41:07,526 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0296 的订单相关状态: {'free_games_used_this_session': 2}
2025-07-15 22:41:07,920 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0296', 'name': '子葵', 'start_time': 1752590466.8901434, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f46a5bad-2909-4bed-bc82-aff35999c962'}, pending_retry_player: None
2025-07-15 22:41:07,924 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:08,260 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:08,260 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:08,260 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:08,260 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:08,260 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:08,260 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:08,260 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 12
2025-07-15 22:41:08,260 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:08,260 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:08,276 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:08,287 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:08,289 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:08,289 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590468.2890122
2025-07-15 22:41:08,290 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:08,290 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:08,291 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:08,291 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:08,293 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:08,309 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:08,309 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:08,313 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:08,313 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:08,313 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:08,429 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0296', 'name': '子葵', 'start_time': 1752590466.8901434, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f46a5bad-2909-4bed-bc82-aff35999c962'}, pending_retry_player: None
2025-07-15 22:41:08,429 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:08,674 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(3) < 目标(4)，添加虚拟玩家。
2025-07-15 22:41:08,674 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['忘记过往(1次)', '子葵(1次)']。已选择: 忘记过往
2025-07-15 22:41:08,690 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 忘记过往 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 22:41:08,945 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0296', 'name': '子葵', 'start_time': 1752590466.8901434, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f46a5bad-2909-4bed-bc82-aff35999c962'}, pending_retry_player: None
2025-07-15 22:41:08,961 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:09,337 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:09,340 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:09,340 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:09,340 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:09,340 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:09,340 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:09,340 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 13
2025-07-15 22:41:09,340 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:09,340 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:09,356 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:09,373 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:09,374 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:09,374 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590469.3744862
2025-07-15 22:41:09,374 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:09,374 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:09,374 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:09,374 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:09,374 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:09,374 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:09,374 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:09,374 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:09,374 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:09,374 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:09,463 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0296', 'name': '子葵', 'start_time': 1752590466.8901434, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f46a5bad-2909-4bed-bc82-aff35999c962'}, pending_retry_player: None
2025-07-15 22:41:09,463 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:09,609 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'f46a5bad-2909-4bed-bc82-aff35999c962', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '2', 'message': '物品_2'}}
2025-07-15 22:41:09,611 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=f46a5bad-2909-4bed-bc82-aff35999c962
2025-07-15 22:41:09,618 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '2', 'message': '物品_2'}
2025-07-15 22:41:09,621 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0296, player_name=子葵, requested_object_id=2
2025-07-15 22:41:09,623 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 2, 物品名称: '物品_2', 原始消息: '物品_2'
2025-07-15 22:41:09,623 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': 'f46a5bad-2909-4bed-bc82-aff35999c962', 'player_id': 'VirtualPlayerID0296', 'player_name': '子葵', 'item_name': '物品_2', 'success': True, 'object_id': '2', 'message': '物品_2', 'source': 'real_mode'}
2025-07-15 22:41:09,623 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 子葵(VirtualPlayerID0296), 成功: True, 物品: 物品_2, 物品ID: 2, 来源: real_mode, ReqID: f46a5bad-2909-4bed-bc82-aff35999c962, 消息: 物品_2
2025-07-15 22:41:09,623 - [StatusUpdateThread] - INFO - 玩家 子葵 本次是第 1 次成功抓取。
2025-07-15 22:41:09,623 - [StatusUpdateThread] - INFO - 为玩家 子葵 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-15 22:41:09,623 - [StatusUpdateThread] - INFO - 显示祝贺特效视频：玩家=子葵, 物品=物品_2
2025-07-15 22:41:09,623 - [StatusUpdateThread] - INFO - 正在连接到OBS WebSocket (host=localhost, port=4455)...
2025-07-15 22:41:09,623 - [StatusUpdateThread] - INFO - Connecting with parameters: host='localhost' port=4455 password='' subs=0 timeout=5
2025-07-15 22:41:09,978 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0296', 'name': '子葵', 'start_time': 1752590466.8901434, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f46a5bad-2909-4bed-bc82-aff35999c962'}, pending_retry_player: None
2025-07-15 22:41:09,978 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:10,392 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:10,392 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:10,392 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:10,392 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:10,392 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:10,392 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:10,392 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 14
2025-07-15 22:41:10,392 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:10,392 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:10,401 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:10,416 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:10,416 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:10,416 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590470.4164124
2025-07-15 22:41:10,416 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:10,416 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:10,416 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:10,432 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:10,432 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:10,432 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:10,432 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:10,432 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:10,432 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:10,432 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:10,496 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0296', 'name': '子葵', 'start_time': 1752590466.8901434, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f46a5bad-2909-4bed-bc82-aff35999c962'}, pending_retry_player: None
2025-07-15 22:41:10,496 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:11,004 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0296', 'name': '子葵', 'start_time': 1752590466.8901434, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f46a5bad-2909-4bed-bc82-aff35999c962'}, pending_retry_player: None
2025-07-15 22:41:11,004 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:11,456 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:11,456 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:11,456 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:11,456 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:11,456 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:11,456 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:11,456 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 15
2025-07-15 22:41:11,456 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:11,456 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:11,456 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:11,456 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:11,456 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:11,471 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590471.4717505
2025-07-15 22:41:11,471 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:11,487 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:11,487 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:11,487 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:11,487 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:11,487 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:11,487 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:11,502 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:11,502 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:11,502 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:11,533 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0296', 'name': '子葵', 'start_time': 1752590466.8901434, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f46a5bad-2909-4bed-bc82-aff35999c962'}, pending_retry_player: None
2025-07-15 22:41:11,533 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:12,040 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0296', 'name': '子葵', 'start_time': 1752590466.8901434, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f46a5bad-2909-4bed-bc82-aff35999c962'}, pending_retry_player: None
2025-07-15 22:41:12,040 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:12,514 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:12,519 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:12,519 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:12,519 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:12,519 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:12,519 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:12,519 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 16
2025-07-15 22:41:12,519 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:12,519 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:12,534 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:12,550 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0296', 'name': '子葵', 'start_time': 1752590466.8901434, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f46a5bad-2909-4bed-bc82-aff35999c962'}, pending_retry_player: None
2025-07-15 22:41:12,550 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:12,550 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:12,550 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:12,550 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590472.5505538
2025-07-15 22:41:12,550 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:12,566 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:12,568 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:12,568 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:12,568 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:12,568 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:12,568 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:12,568 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:12,568 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:12,568 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:13,072 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0296', 'name': '子葵', 'start_time': 1752590466.8901434, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f46a5bad-2909-4bed-bc82-aff35999c962'}, pending_retry_player: None
2025-07-15 22:41:13,073 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:13,574 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:13,574 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:13,577 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:13,577 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0296', 'name': '子葵', 'start_time': 1752590466.8901434, 'target_id': '2', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'f46a5bad-2909-4bed-bc82-aff35999c962'}, pending_retry_player: None
2025-07-15 22:41:13,577 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:13,578 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:13,578 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:13,579 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:13,580 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 17
2025-07-15 22:41:13,580 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:13,581 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:13,594 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:13,602 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:13,606 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:13,608 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590473.6080365
2025-07-15 22:41:13,609 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:13,609 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:13,610 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:13,611 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:13,612 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:13,612 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:13,613 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:13,613 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:13,615 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:13,621 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:13,677 - [StatusUpdateThread] - ERROR - ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Program Files\Python39\lib\site-packages\obsws_python\baseclient.py", line 41, in __init__
    self.ws.connect(f"ws://{self.host}:{self.port}", timeout=self.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_core.py", line 256, in connect
    self.sock, addrs = connect(
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 145, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 232, in _open_socket
    raise err
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 209, in _open_socket
    sock.connect(address)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-15 22:41:13,677 - [StatusUpdateThread] - ERROR - 连接到OBS WebSocket失败: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-15 22:41:13,732 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'f46a5bad-2909-4bed-bc82-aff35999c962', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-15 22:41:13,734 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=f46a5bad-2909-4bed-bc82-aff35999c962
2025-07-15 22:41:13,734 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-15 22:41:13,736 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0296, player_name=子葵, requested_object_id=2
2025-07-15 22:41:13,736 - [MoveServiceEventReceiver] - INFO - 移动服务请求 f46a5bad-2909-4bed-bc82-aff35999c962 (cycle_completed) 已完成，从活动命令中移除。
2025-07-15 22:41:13,827 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 子葵(VirtualPlayerID0296)
2025-07-15 22:41:13,827 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_2'，准备结束游戏。
2025-07-15 22:41:13,827 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 子葵(VirtualPlayerID0296), 结果: 物品_2, 订单: None
2025-07-15 22:41:13,827 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-15 22:41:13,827 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-15 22:41:14,092 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {}, pending_retry_player: None
2025-07-15 22:41:14,092 - [GameProcessThread] - INFO - [游戏线程] 玩家 仙鹤(VirtualPlayerID0297) 开始免费游戏，扣除免费次数 (1)
2025-07-15 22:41:14,092 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 仙鹤(VirtualPlayerID0297) 确保抓中, z_offset_extra=0
2025-07-15 22:41:14,092 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=5cffbd59-ab4a-41ce-af65-f3c2e07888c7, Player=仙鹤, Target=3, Z_Offset_Extra=0.0
2025-07-15 22:41:14,092 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '5cffbd59-ab4a-41ce-af65-f3c2e07888c7', 'status': 'queued', 'message': '抓取物体 3 指令已加入队列'}
2025-07-15 22:41:14,092 - [GameProcessThread] - INFO - [游戏线程] 玩家 仙鹤(VirtualPlayerID0297) 抓取指令已发送到移动服务，命令ID: 5cffbd59-ab4a-41ce-af65-f3c2e07888c7
2025-07-15 22:41:14,092 - [MoveServiceEventReceiver] - INFO - 抓取指令 5cffbd59-ab4a-41ce-af65-f3c2e07888c7 已被移动服务接受并加入队列
2025-07-15 22:41:14,179 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.036秒 - 玩家: VirtualPlayerID0296, 结果: 物品_2, 订单: NULL
2025-07-15 22:41:14,181 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0296] = 1
2025-07-15 22:41:14,199 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0297 订单状态: {'free_games_used_this_session': 1}
2025-07-15 22:41:14,201 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0297 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-15 22:41:14,611 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590474.0921717, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5cffbd59-ab4a-41ce-af65-f3c2e07888c7'}, pending_retry_player: None
2025-07-15 22:41:14,613 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:14,626 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:14,629 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:14,630 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:14,630 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:14,631 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:14,631 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:14,631 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 18
2025-07-15 22:41:14,632 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:14,632 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:14,637 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:14,639 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:14,640 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:14,641 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590474.6418931
2025-07-15 22:41:14,641 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:14,643 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:14,650 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:14,651 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:14,657 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:14,659 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:14,660 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:14,660 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:14,661 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:14,663 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:14,744 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 仙鹤 已完成游戏，总游戏次数: 1
2025-07-15 22:41:14,747 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(3) < 目标(6)，添加虚拟玩家。
2025-07-15 22:41:14,751 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['子葵(1次)', '仙鹤(1次)']。已选择: 子葵
2025-07-15 22:41:14,755 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 子葵 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 22:41:15,118 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590474.0921717, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5cffbd59-ab4a-41ce-af65-f3c2e07888c7'}, pending_retry_player: None
2025-07-15 22:41:15,119 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:15,624 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590474.0921717, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5cffbd59-ab4a-41ce-af65-f3c2e07888c7'}, pending_retry_player: None
2025-07-15 22:41:15,624 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:15,671 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:15,676 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:15,676 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:15,692 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:15,692 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:15,692 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:15,707 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 19
2025-07-15 22:41:15,707 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:15,707 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:15,707 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:15,707 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:15,707 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:15,707 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590475.7077622
2025-07-15 22:41:15,707 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:15,719 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:15,719 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:15,719 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:15,721 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:15,721 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:15,722 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:15,722 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:15,722 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:15,727 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:16,148 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590474.0921717, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5cffbd59-ab4a-41ce-af65-f3c2e07888c7'}, pending_retry_player: None
2025-07-15 22:41:16,149 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:16,362 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '5cffbd59-ab4a-41ce-af65-f3c2e07888c7', 'event_name': 'hardware_error', 'data': {'error_message': '检测到电机硬件故障，已终止本轮操作', 'faults': [{'axis': 'motor_x', 'fault_type': 'hardware_fault', 'fault_msg': '模拟硬件故障（测试）'}, {'axis': 'motor_z', 'fault_type': 'timeout_fault', 'fault_msg': '模拟超时故障（测试）'}], 'player_id': 'VirtualPlayerID0297', 'player_name': '仙鹤'}}
2025-07-15 22:41:16,362 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='hardware_error', request_id=5cffbd59-ab4a-41ce-af65-f3c2e07888c7
2025-07-15 22:41:16,362 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'error_message': '检测到电机硬件故障，已终止本轮操作', 'faults': [{'axis': 'motor_x', 'fault_type': 'hardware_fault', 'fault_msg': '模拟硬件故障（测试）'}, {'axis': 'motor_z', 'fault_type': 'timeout_fault', 'fault_msg': '模拟超时故障（测试）'}], 'player_id': 'VirtualPlayerID0297', 'player_name': '仙鹤'}
2025-07-15 22:41:16,378 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0297, player_name=仙鹤, requested_object_id=3
2025-07-15 22:41:16,378 - [MoveServiceEventReceiver] - INFO - [事件转换] 处理其他运动模块事件: 'hardware_error'
2025-07-15 22:41:16,378 - [MoveServiceEventReceiver] - DEBUG - [事件发送] 通用移动服务事件 'hardware_error' 已发送: {'type': 'move_service_event', 'request_id': '5cffbd59-ab4a-41ce-af65-f3c2e07888c7', 'player_id': 'VirtualPlayerID0297', 'player_name': '仙鹤', 'requested_object_id': '3', 'event_name': 'hardware_error', 'data': {'error_message': '检测到电机硬件故障，已终止本轮操作', 'faults': [{'axis': 'motor_x', 'fault_type': 'hardware_fault', 'fault_msg': '模拟硬件故障（测试）'}, {'axis': 'motor_z', 'fault_type': 'timeout_fault', 'fault_msg': '模拟超时故障（测试）'}], 'player_id': 'VirtualPlayerID0297', 'player_name': '仙鹤'}}
2025-07-15 22:41:16,378 - [StatusUpdateThread] - ERROR - [移动服务端故障] hardware_error for 仙鹤(VirtualPlayerID0297), request_id=5cffbd59-ab4a-41ce-af65-f3c2e07888c7, data={"error_message": "检测到电机硬件故障，已终止本轮操作", "faults": [{"axis": "motor_x", "fault_type": "hardware_fault", "fault_msg": "模拟硬件故障（测试）"}, {"axis": "motor_z", "fault_type": "timeout_fault", "fault_msg": "模拟超时故障（测试）"}], "player_id": "VirtualPlayerID0297", "player_name": "仙鹤"}
2025-07-15 22:41:16,391 - [StatusUpdateThread] - ERROR - [硬件故障详情] axis=motor_x, fault_type=hardware_fault, fault_msg=模拟硬件故障（测试）
2025-07-15 22:41:16,392 - [StatusUpdateThread] - ERROR - [硬件故障详情] axis=motor_z, fault_type=timeout_fault, fault_msg=模拟超时故障（测试）
2025-07-15 22:41:16,392 - [StatusUpdateThread] - WARNING - [重试机制] 玩家 仙鹤(VirtualPlayerID0297) 已进入pending_retry_player，等待移动服务端恢复后重试。
2025-07-15 22:41:16,663 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {}, pending_retry_player: {'entry_tuple': (['', 1752590474.0921717, '仙鹤', 'VirtualPlayerID0297', '3', '', '小熊玩偶'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590474.0921717, 'target_id': '3', 'target_object': '小熊玩偶', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5cffbd59-ab4a-41ce-af65-f3c2e07888c7'}}
2025-07-15 22:41:16,663 - [GameProcessThread] - INFO - [重试机制] 检测到pending_retry_player，优先重试玩家: 仙鹤(VirtualPlayerID0297)
2025-07-15 22:41:16,663 - [GameProcessThread] - INFO - [游戏线程] 玩家 仙鹤(VirtualPlayerID0297) 开始免费游戏，扣除免费次数 (2)
2025-07-15 22:41:16,663 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 仙鹤(VirtualPlayerID0297) 确保抓中, z_offset_extra=0
2025-07-15 22:41:16,678 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=9451afa9-e5ef-4f39-8bce-ab8753fae9b9, Player=仙鹤, Target=3, Z_Offset_Extra=0.0
2025-07-15 22:41:16,678 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9', 'status': 'queued', 'message': '抓取物体 3 指令已加入队列'}
2025-07-15 22:41:16,678 - [GameProcessThread] - INFO - [游戏线程] 玩家 仙鹤(VirtualPlayerID0297) 抓取指令已发送到移动服务，命令ID: 9451afa9-e5ef-4f39-8bce-ab8753fae9b9
2025-07-15 22:41:16,694 - [MoveServiceEventReceiver] - INFO - 抓取指令 9451afa9-e5ef-4f39-8bce-ab8753fae9b9 已被移动服务接受并加入队列
2025-07-15 22:41:16,742 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:16,742 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:16,742 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:16,742 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:16,742 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:16,742 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:16,742 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 20
2025-07-15 22:41:16,742 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:16,742 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:16,756 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:16,760 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:16,764 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:16,764 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590476.7644334
2025-07-15 22:41:16,765 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:16,765 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:16,765 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:16,766 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:16,767 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:16,768 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:16,768 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:16,769 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:16,769 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:16,770 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:16,773 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0297 订单状态: {'free_games_used_this_session': 2}
2025-07-15 22:41:16,795 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0297 的订单相关状态: {'free_games_used_this_session': 2}
2025-07-15 22:41:16,816 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-15 22:41:16,870 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 5 个玩家, 成功更新 5 条记录.
2025-07-15 22:41:17,220 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590476.6631358, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9'}, pending_retry_player: None
2025-07-15 22:41:17,223 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:17,726 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590476.6631358, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9'}, pending_retry_player: None
2025-07-15 22:41:17,727 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:17,785 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:17,785 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:17,785 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:17,785 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:17,785 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:17,785 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:17,785 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 21
2025-07-15 22:41:17,785 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:17,785 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:17,812 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:17,816 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:17,817 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:17,819 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590477.819247
2025-07-15 22:41:17,819 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:17,820 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:17,820 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:17,820 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:17,822 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:17,822 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:17,823 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 22:41:17,823 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:17,824 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:17,825 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:18,243 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590476.6631358, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9'}, pending_retry_player: None
2025-07-15 22:41:18,243 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:18,765 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590476.6631358, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9'}, pending_retry_player: None
2025-07-15 22:41:18,768 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:18,829 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:18,829 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:18,838 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:18,839 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:18,840 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:18,841 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:18,842 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 22
2025-07-15 22:41:18,842 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:18,843 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:18,862 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:18,862 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:18,869 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:18,869 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590478.869808
2025-07-15 22:41:18,870 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:18,870 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:18,871 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:18,871 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:18,872 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:18,873 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:18,873 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:18,874 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:18,874 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:18,875 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:19,272 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590476.6631358, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9'}, pending_retry_player: None
2025-07-15 22:41:19,272 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:19,468 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '3', 'message': '物品_3'}}
2025-07-15 22:41:19,468 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=9451afa9-e5ef-4f39-8bce-ab8753fae9b9
2025-07-15 22:41:19,483 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '3', 'message': '物品_3'}
2025-07-15 22:41:19,483 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0297, player_name=仙鹤, requested_object_id=3
2025-07-15 22:41:19,483 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 3, 物品名称: '物品_3', 原始消息: '物品_3'
2025-07-15 22:41:19,483 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9', 'player_id': 'VirtualPlayerID0297', 'player_name': '仙鹤', 'item_name': '物品_3', 'success': True, 'object_id': '3', 'message': '物品_3', 'source': 'real_mode'}
2025-07-15 22:41:19,483 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 仙鹤(VirtualPlayerID0297), 成功: True, 物品: 物品_3, 物品ID: 3, 来源: real_mode, ReqID: 9451afa9-e5ef-4f39-8bce-ab8753fae9b9, 消息: 物品_3
2025-07-15 22:41:19,483 - [StatusUpdateThread] - INFO - 玩家 仙鹤 本次是第 1 次成功抓取。
2025-07-15 22:41:19,483 - [StatusUpdateThread] - INFO - 为玩家 仙鹤 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-15 22:41:19,483 - [StatusUpdateThread] - INFO - 显示祝贺特效视频：玩家=仙鹤, 物品=物品_3
2025-07-15 22:41:19,483 - [StatusUpdateThread] - INFO - 正在连接到OBS WebSocket (host=localhost, port=4455)...
2025-07-15 22:41:19,490 - [StatusUpdateThread] - INFO - Connecting with parameters: host='localhost' port=4455 password='' subs=0 timeout=5
2025-07-15 22:41:19,779 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590476.6631358, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9'}, pending_retry_player: None
2025-07-15 22:41:19,779 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:19,877 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:19,877 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:19,877 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:19,877 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:19,877 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:19,883 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:19,883 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 23
2025-07-15 22:41:19,883 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:19,884 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:19,884 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:19,884 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:19,884 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:19,884 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590479.8849382
2025-07-15 22:41:19,884 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:19,884 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:19,884 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:19,884 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:19,884 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:19,884 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:19,884 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 22:41:19,884 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:19,884 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:19,884 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:20,285 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590476.6631358, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9'}, pending_retry_player: None
2025-07-15 22:41:20,285 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:20,801 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590476.6631358, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9'}, pending_retry_player: None
2025-07-15 22:41:20,801 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:20,899 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:20,899 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:20,899 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:20,899 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:20,899 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:20,899 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:20,899 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 24
2025-07-15 22:41:20,899 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:20,899 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:20,913 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:20,931 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:20,931 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:20,931 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590480.9317062
2025-07-15 22:41:20,931 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:20,931 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:20,931 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:20,931 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:20,931 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:20,931 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:20,931 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 22:41:20,931 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:20,931 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:20,947 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:21,322 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590476.6631358, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9'}, pending_retry_player: None
2025-07-15 22:41:21,332 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:21,858 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590476.6631358, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9'}, pending_retry_player: None
2025-07-15 22:41:21,858 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:21,973 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:21,973 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:21,973 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:21,973 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:21,973 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:21,973 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:21,973 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 25
2025-07-15 22:41:21,973 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:21,973 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:21,989 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:21,989 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:21,989 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:21,998 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590481.998374
2025-07-15 22:41:21,998 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:21,999 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:21,999 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:22,000 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:22,001 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:22,001 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:22,001 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:22,001 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:22,016 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:22,016 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:22,385 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590476.6631358, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9'}, pending_retry_player: None
2025-07-15 22:41:22,385 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:22,898 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590476.6631358, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9'}, pending_retry_player: None
2025-07-15 22:41:22,898 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:23,037 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:23,037 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:23,052 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:23,052 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:23,052 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:23,052 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:23,052 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 26
2025-07-15 22:41:23,052 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:23,052 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:23,052 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:23,068 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:23,083 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:23,091 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590483.090322
2025-07-15 22:41:23,093 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:23,095 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:23,097 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:23,097 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:23,097 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:23,097 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:23,097 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:23,097 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:23,097 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:23,097 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:23,416 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0297', 'name': '仙鹤', 'start_time': 1752590476.6631358, 'target_id': '3', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9'}, pending_retry_player: None
2025-07-15 22:41:23,416 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:23,552 - [StatusUpdateThread] - ERROR - ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Program Files\Python39\lib\site-packages\obsws_python\baseclient.py", line 41, in __init__
    self.ws.connect(f"ws://{self.host}:{self.port}", timeout=self.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_core.py", line 256, in connect
    self.sock, addrs = connect(
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 145, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 232, in _open_socket
    raise err
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 209, in _open_socket
    sock.connect(address)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-15 22:41:23,558 - [StatusUpdateThread] - ERROR - 连接到OBS WebSocket失败: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-15 22:41:23,666 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '9451afa9-e5ef-4f39-8bce-ab8753fae9b9', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-15 22:41:23,670 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=9451afa9-e5ef-4f39-8bce-ab8753fae9b9
2025-07-15 22:41:23,671 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-15 22:41:23,671 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0297, player_name=仙鹤, requested_object_id=3
2025-07-15 22:41:23,671 - [MoveServiceEventReceiver] - INFO - 移动服务请求 9451afa9-e5ef-4f39-8bce-ab8753fae9b9 (cycle_completed) 已完成，从活动命令中移除。
2025-07-15 22:41:23,817 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 仙鹤(VirtualPlayerID0297)
2025-07-15 22:41:23,818 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_3'，准备结束游戏。
2025-07-15 22:41:23,820 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 仙鹤(VirtualPlayerID0297), 结果: 物品_3, 订单: None
2025-07-15 22:41:23,820 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-15 22:41:23,820 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-15 22:41:23,937 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {}, pending_retry_player: None
2025-07-15 22:41:23,939 - [GameProcessThread] - INFO - [游戏线程] 玩家 小邪恶(VirtualPlayerID0298) 开始免费游戏，扣除免费次数 (1)
2025-07-15 22:41:23,940 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 小邪恶(VirtualPlayerID0298) 确保抓中, z_offset_extra=0
2025-07-15 22:41:23,941 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=6c78f427-c314-4c22-8c71-619088574b6d, Player=小邪恶, Target=5, Z_Offset_Extra=0.0
2025-07-15 22:41:23,942 - [GameProcessThread] - INFO - [游戏线程] 玩家 小邪恶(VirtualPlayerID0298) 抓取指令已发送到移动服务，命令ID: 6c78f427-c314-4c22-8c71-619088574b6d
2025-07-15 22:41:23,943 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '6c78f427-c314-4c22-8c71-619088574b6d', 'status': 'queued', 'message': '抓取物体 5 指令已加入队列'}
2025-07-15 22:41:23,944 - [MoveServiceEventReceiver] - INFO - 抓取指令 6c78f427-c314-4c22-8c71-619088574b6d 已被移动服务接受并加入队列
2025-07-15 22:41:24,002 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.041秒 - 玩家: VirtualPlayerID0297, 结果: 物品_3, 订单: NULL
2025-07-15 22:41:24,002 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0297] = 1
2025-07-15 22:41:24,028 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0298 订单状态: {'free_games_used_this_session': 1}
2025-07-15 22:41:24,032 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0298 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-15 22:41:24,117 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:24,121 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:24,122 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:24,123 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:24,123 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:24,124 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:24,125 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 27
2025-07-15 22:41:24,125 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:24,126 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:24,130 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:24,135 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:24,142 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:24,144 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590484.144224
2025-07-15 22:41:24,146 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:24,147 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:24,150 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:24,166 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:24,166 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:24,166 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:24,166 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:24,166 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:24,166 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:24,166 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:24,452 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0298', 'name': '小邪恶', 'start_time': 1752590483.9397364, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6c78f427-c314-4c22-8c71-619088574b6d'}, pending_retry_player: None
2025-07-15 22:41:24,452 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:24,814 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 小邪恶 已完成游戏，总游戏次数: 1
2025-07-15 22:41:24,817 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(3) < 目标(5)，添加虚拟玩家。
2025-07-15 22:41:24,819 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['仙鹤(1次)', '小邪恶(1次)']。已选择: 仙鹤
2025-07-15 22:41:24,820 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 仙鹤 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 22:41:24,966 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0298', 'name': '小邪恶', 'start_time': 1752590483.9397364, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6c78f427-c314-4c22-8c71-619088574b6d'}, pending_retry_player: None
2025-07-15 22:41:24,968 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:25,192 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:25,196 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:25,196 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:25,196 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:25,197 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:25,197 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:25,198 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 28
2025-07-15 22:41:25,198 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:25,199 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:25,203 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:25,205 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:25,207 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:25,207 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590485.207396
2025-07-15 22:41:25,207 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:25,207 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:25,207 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:25,207 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:25,223 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:25,223 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:25,223 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:25,223 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:25,223 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:25,223 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:25,472 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0298', 'name': '小邪恶', 'start_time': 1752590483.9397364, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6c78f427-c314-4c22-8c71-619088574b6d'}, pending_retry_player: None
2025-07-15 22:41:25,472 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 22:41:25,708 - [MoveServiceEventReceiver] - WARNING - 移动服务事件接收时连接错误（忽略，保持长连）: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-15 22:41:25,724 - [StatusUpdateThread] - ERROR - [移动服务端故障] 移动服务端连接断开，当前玩家将进入pending_retry_player。
2025-07-15 22:41:25,724 - [StatusUpdateThread] - WARNING - [重试机制] 玩家 小邪恶(VirtualPlayerID0298) 已进入pending_retry_player，等待移动服务端恢复后重试。
2025-07-15 22:41:25,982 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {}, pending_retry_player: {'entry_tuple': (['', 1752590483.9397364, '小邪恶', 'VirtualPlayerID0298', '5', '', '毛绒兔子'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0298', 'name': '小邪恶', 'start_time': 1752590483.9397364, 'target_id': '5', 'target_object': '毛绒兔子', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6c78f427-c314-4c22-8c71-619088574b6d'}}
2025-07-15 22:41:25,997 - [GameProcessThread] - INFO - [重试机制] 检测到pending_retry_player，优先重试玩家: 小邪恶(VirtualPlayerID0298)
2025-07-15 22:41:25,997 - [GameProcessThread] - INFO - [游戏线程] 玩家 小邪恶(VirtualPlayerID0298) 开始免费游戏，扣除免费次数 (2)
2025-07-15 22:41:25,997 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 小邪恶(VirtualPlayerID0298) 确保抓中, z_offset_extra=0
2025-07-15 22:41:26,013 - [GameProcessThread] - ERROR - 发送抓取指令时发生套接字错误: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-15 22:41:26,013 - [GameProcessThread] - INFO - 正在断开与移动服务的连接...
2025-07-15 22:41:26,013 - [GameProcessThread] - INFO - 与移动服务的连接已断开。
2025-07-15 22:41:26,013 - [MoveServiceEventReceiver] - INFO - 移动服务连接断开，将在 1.0 秒后尝试重连...
2025-07-15 22:41:26,013 - [GameProcessThread] - ERROR - [游戏线程] 玩家 小邪恶(VirtualPlayerID0298) 发送抓取指令失败，移动服务端可能已断开，当前玩家将进入pending_retry_player，等待服务端恢复后重试。
2025-07-15 22:41:26,078 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0298 订单状态: {'free_games_used_this_session': 2}
2025-07-15 22:41:26,097 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0298 的订单相关状态: {'free_games_used_this_session': 2}
2025-07-15 22:41:26,244 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:26,251 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:26,255 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:26,259 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:26,262 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:26,263 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:26,265 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 29
2025-07-15 22:41:26,266 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:26,266 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:26,271 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:26,287 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:26,287 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:26,287 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590486.2872086
2025-07-15 22:41:26,287 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:26,287 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:26,287 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:26,287 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:26,287 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:26,287 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 22:41:26,287 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 22:41:26,287 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 22:41:26,287 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:26,287 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 22:41:26,611 - [HealthCheckThread] - DEBUG - [健康检查] 休眠结束
2025-07-15 22:41:26,615 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-15 22:41:26,616 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 0.3 秒
2025-07-15 22:41:26,617 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-15 22:41:26,802 - [MainThread] - INFO - 收到终止信号，标志置 False，主线程将退出并在 finally 做清理
2025-07-15 22:41:26,802 - [MainThread] - INFO - 进入清理阶段...
2025-07-15 22:41:26,808 - [MainThread] - INFO - [清理] 开始执行清理操作
2025-07-15 22:41:26,809 - [MainThread] - DEBUG - [清理] 设置 stop_flag['running'] = False
2025-07-15 22:41:26,809 - [MainThread] - INFO - [清理] 阶段1: 停止所有外部输入...
2025-07-15 22:41:26,809 - [MainThread] - DEBUG - [清理] 停止消息获取线程...
2025-07-15 22:41:26,810 - [MainThread] - DEBUG - [消息线程] 尝试停止消息线程
2025-07-15 22:41:26,810 - [MainThread] - DEBUG - [消息线程] 等待消息线程结束
2025-07-15 22:41:26,836 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 管理器线程已停止。
2025-07-15 22:41:26,843 - [DetectionReceiver] - INFO - 已断开检测服务器连接
2025-07-15 22:41:26,845 - [DetectionReceiver] - INFO - 检测数据接收器已停止
2025-07-15 22:41:27,135 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-15 22:41:27,160 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 5 个玩家, 成功更新 5 条记录.
2025-07-15 22:41:27,310 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 30
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752590487.3116498
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 22:41:27,311 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 22:41:27,334 - [MessagePollThread] - DEBUG - [消息线程] 消息获取线程结束
2025-07-15 22:41:27,335 - [MessagePollThread] - DEBUG - [消息流] 停止健康检查
2025-07-15 22:41:27,335 - [MessagePollThread] - DEBUG - [健康检查] 收到停止信号
2025-07-15 22:41:27,335 - [MessagePollThread] - DEBUG - [健康检查] 等待线程结束
2025-07-15 22:41:28,822 - [MainThread] - WARNING - [消息线程] 消息线程未在2秒内结束
2025-07-15 22:41:28,822 - [MainThread] - DEBUG - [消息线程] 消息队列已清空
2025-07-15 22:41:28,822 - [MainThread] - DEBUG - [清理] 停止移动服务客户端...
2025-07-15 22:41:28,822 - [MainThread] - INFO - 正在停止移动服务客户端...
2025-07-15 22:41:28,822 - [MainThread] - INFO - 正在断开与移动服务的连接...
2025-07-15 22:41:28,822 - [MainThread] - DEBUG - 关闭套接字时出错 (shutdown): [WinError 10057] 由于套接字没有连接并且(当使用一个 sendto 调用发送数据报套接字时)没有提供地址，发送或接收数据的请求没有被接受。
2025-07-15 22:41:28,822 - [MoveServiceEventReceiver] - ERROR - 连接移动服务失败: [WinError 10038] 在一个非套接字上尝试了一个操作。
2025-07-15 22:41:28,837 - [MainThread] - INFO - 与移动服务的连接已断开。
2025-07-15 22:41:28,837 - [MoveServiceEventReceiver] - INFO - 移动服务事件接收线程已停止。
2025-07-15 22:41:28,847 - [MoveServiceEventReceiver] - INFO - 正在断开与移动服务的连接...
2025-07-15 22:41:28,847 - [MoveServiceEventReceiver] - INFO - 与移动服务的连接已断开。
2025-07-15 22:41:28,847 - [MainThread] - INFO - 移动服务客户端已停止。
2025-07-15 22:41:28,847 - [MainThread] - DEBUG - [清理] 断开检测服务器连接...
2025-07-15 22:41:28,847 - [MainThread] - INFO - 已断开检测服务器连接
2025-07-15 22:41:28,847 - [MainThread] - INFO - [清理] 阶段2: 停止独立进程...
2025-07-15 22:41:28,851 - [MainThread] - DEBUG - [清理] 关闭GUI通信队列...
2025-07-15 22:41:28,851 - [MainThread] - DEBUG - [清理] 停止Web显示进程...
2025-07-15 22:41:29,113 - [MainThread] - INFO - [清理] 阶段3: 等待内部线程结束...
2025-07-15 22:41:29,113 - [MainThread] - DEBUG - [清理] 等待主循环监控线程结束…
2025-07-15 22:41:29,341 - [MessagePollThread] - INFO - 健康检查线程已停止
2025-07-15 22:41:31,121 - [MainThread] - WARNING - [清理] 主循环监控线程未在2秒内结束
2025-07-15 22:41:31,121 - [MainThread] - INFO - [清理] 阶段4: 最终数据同步和资源释放...
2025-07-15 22:41:31,121 - [MainThread] - DEBUG - [清理] 停止数据库同步管理器...
2025-07-15 22:41:31,121 - [MainThread] - INFO - 停止数据库同步线程
2025-07-15 22:41:31,190 - [MainThread] - INFO - 数据库同步线程已停止
2025-07-15 22:41:31,190 - [MainThread] - ERROR - 更新源 '抓中特效' 可见性时出错: 'NoneType' object has no attribute 'get_current_program_scene'
2025-07-15 22:41:31,190 - [MainThread] - DEBUG - [清理] 强制隐藏 OBS 视频源
2025-07-15 22:41:31,190 - [MainThread] - DEBUG - [清理] 停止健康检查线程...
2025-07-15 22:41:31,190 - [MainThread] - DEBUG - [健康检查] 收到停止信号
2025-07-15 22:41:31,190 - [MainThread] - DEBUG - [健康检查] 等待线程结束
2025-07-15 22:41:33,195 - [MainThread] - INFO - 健康检查线程已停止
2025-07-15 22:41:33,195 - [MainThread] - DEBUG - [清理] 已获取队列锁，执行最终数据同步...
2025-07-15 22:41:33,210 - [MainThread] - INFO - 数据库操作: 更新队列完成，耗时 0.016秒
2025-07-15 22:41:35,215 - [MainThread] - WARNING - 获取快照时未能获取 queue_lock，使用上次快照队列数据
2025-07-15 22:41:35,215 - [MainThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-15 22:41:35,215 - [MainThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 5 个玩家, 成功更新 5 条记录.
2025-07-15 22:41:35,215 - [MainThread] - INFO - [清理] 最终数据同步已完成
2025-07-15 22:41:35,215 - [MainThread] - DEBUG - [清理] 关闭数据库会话...
2025-07-15 22:41:35,231 - [MainThread] - INFO - 已关闭会话，ID: 1
2025-07-15 22:41:35,247 - [MainThread] - INFO - [清理] 数据库会话已关闭
2025-07-15 22:41:35,247 - [MainThread] - DEBUG - [清理] 队列锁已释放
2025-07-15 22:41:35,247 - [MainThread] - INFO - [清理] 清理操作成功
2025-07-15 22:41:35,316 - [MainThread] - INFO - 程序退出
