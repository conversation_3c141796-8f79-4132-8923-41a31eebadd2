2025-07-15 23:11:29,634 - [MainThread] - INFO - 日志系统初始化完成
2025-07-15 23:11:29,634 - [MainThread] - INFO - 应用程序启动
2025-07-15 23:11:30,368 - [MainThread] - INFO - 信号处理器设置完成（仅置标志）
2025-07-15 23:11:30,368 - [MainThread] - INFO - 信号处理器已设置(使用共享状态字典)
2025-07-15 23:11:32,618 - [MainThread] - INFO - Web显示进程启动成功
2025-07-15 23:11:32,834 - [MainThread] - INFO - GUI 进程已通过 multiprocessing.Process 启动, PID: 13876
2025-07-15 23:11:32,834 - [MainThread] - INFO - 每个会话最大免费游戏次数: 10
2025-07-15 23:11:32,834 - [MainThread] - INFO - 初始化全局 OBSController
2025-07-15 23:11:32,834 - [MainThread] - INFO - 正在连接到OBS WebSocket (host=localhost, port=4455)...
2025-07-15 23:11:32,834 - [MainThread] - INFO - Connecting with parameters: host='localhost' port=4455 password='' subs=0 timeout=5
2025-07-15 23:11:36,874 - [MainThread] - ERROR - ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Program Files\Python39\lib\site-packages\obsws_python\baseclient.py", line 41, in __init__
    self.ws.connect(f"ws://{self.host}:{self.port}", timeout=self.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_core.py", line 256, in connect
    self.sock, addrs = connect(
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 145, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 232, in _open_socket
    raise err
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 209, in _open_socket
    sock.connect(address)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-15 23:11:36,874 - [MainThread] - ERROR - 连接到OBS WebSocket失败: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-15 23:11:36,874 - [MainThread] - INFO - 检查历史会话记录...
2025-07-15 23:11:36,874 - [MainThread] - INFO - 未找到历史会话记录，将创建新会话
2025-07-15 23:11:36,890 - [MainThread] - INFO - 已创建新会话，ID: 1
2025-07-15 23:11:36,890 - [MainThread] - INFO - 当前使用会话 ID: 1
2025-07-15 23:11:36,890 - [MainThread] - DEBUG - 当前会话ID已设置为: 1
2025-07-15 23:11:36,912 - [MainThread] - INFO - 会话开始时间: None
2025-07-15 23:11:36,912 - [MainThread] - INFO - 使用新创建的会话，初始化空数据结构
2025-07-15 23:11:36,912 - [MainThread] - INFO - 初始化全局数据库同步管理器
2025-07-15 23:11:36,912 - [MainThread] - INFO - 启动数据库同步线程，同步间隔: 10秒
2025-07-15 23:11:36,912 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-15 23:11:36,912 - [MainThread] - INFO - 会话初始化完成，新会话，会话ID: 1
2025-07-15 23:11:36,912 - [DBSyncThread] - DEBUG - [全量同步] player_info 为空，跳过 comments_after_game 同步。
2025-07-15 23:11:36,912 - [MainThread] - INFO - 成功连接到移动服务 localhost:5556
2025-07-15 23:11:36,912 - [MoveServiceEventReceiver] - INFO - 移动服务事件接收线程已启动。
2025-07-15 23:11:36,912 - [MainThread] - INFO - 移动服务客户端已启动，连接到: localhost:5556
2025-07-15 23:11:36,928 - [MainThread] - INFO - 检测数据接收器线程已启动
2025-07-15 23:11:36,928 - [DetectionReceiver] - INFO - 检测数据接收器启动
2025-07-15 23:11:36,928 - [MainThread] - INFO - [虚拟玩家] 成功加载 300 个虚拟玩家，起始索引: 169
2025-07-15 23:11:36,928 - [MainThread] - INFO - [虚拟玩家] 管理器启动...
2025-07-15 23:11:36,928 - [MainThread] - INFO - 虚拟玩家管理器已启动
2025-07-15 23:11:36,928 - [MainThread] - INFO - 状态更新处理线程已启动
2025-07-15 23:11:36,928 - [MainThread] - INFO - GUI的FPS发送线程已启动
2025-07-15 23:11:36,928 - [MainThread] - INFO - 游戏处理线程已启动
2025-07-15 23:11:36,928 - [DetectionReceiver] - INFO - 成功连接到检测服务器 localhost:5555
2025-07-15 23:11:36,928 - [MainThread] - INFO - 主循环监控线程已启动
2025-07-15 23:11:36,928 - [StatusUpdateThread] - INFO - [重试机制] 检测到移动服务端已恢复，设置移动服务为就绪状态。
2025-07-15 23:11:36,928 - [DetectionReceiver] - DEBUG - 已发送订阅命令
2025-07-15 23:11:36,928 - [MainThread] - INFO - [消息线程] 启动消息获取后台线程
2025-07-15 23:11:36,928 - [StatusUpdateThread] - INFO - [重试机制] 移动服务已设为就绪状态，pending_retry_player将在下次循环中被处理
2025-07-15 23:11:36,928 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-15 23:11:36,928 - [DetectionReceiver] - INFO - 收到订阅确认: Subscription successful for client 11
2025-07-15 23:11:36,928 - [MessagePollThread] - DEBUG - [消息线程] 开始运行消息获取线程
2025-07-15 23:11:36,943 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-15 23:11:36,928 - [MainThread] - INFO - [消息线程] 消息获取线程已启动
2025-07-15 23:11:36,943 - [MessagePollThread] - DEBUG - [消息流] 启动健康检查
2025-07-15 23:11:36,943 - [MainThread] - INFO - 消息获取后台线程已启动，开始接收消息...
2025-07-15 23:11:36,943 - [MainThread] - INFO - 开始处理消息，按Ctrl+C停止...
2025-07-15 23:11:36,943 - [HealthCheckThread] - DEBUG - [健康检查] 任务开始
2025-07-15 23:11:36,943 - [MessagePollThread] - INFO - 健康检查线程已启动，间隔: 30秒
2025-07-15 23:11:36,943 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-15 23:11:36,943 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:36,943 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 7.6 秒
2025-07-15 23:11:36,943 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:36,943 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-15 23:11:36,943 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:36,943 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:36,943 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 1
2025-07-15 23:11:36,943 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:36,943 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:36,959 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:36,959 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:36,959 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:36,959 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: **********.9592774
2025-07-15 23:11:36,974 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:36,974 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:36,974 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:36,990 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:36,990 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:37,006 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:37,006 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:37,006 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=0.0s, 心跳间隔=1s
2025-07-15 23:11:37,006 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:37,024 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,071 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,107 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,161 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,199 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,249 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,292 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,328 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,365 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,406 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,440 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,464 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-15 23:11:37,466 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-15 23:11:37,492 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,537 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,584 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,628 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,667 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,704 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,739 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,790 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,831 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,878 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,925 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,971 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-15 23:11:37,972 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:37,974 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-15 23:11:38,014 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,030 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:38,031 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:38,032 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:38,033 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:38,033 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:38,035 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:38,035 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 2
2025-07-15 23:11:38,036 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:38,036 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:38,041 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:38,043 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:38,049 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,047 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:38,054 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592298.054793
2025-07-15 23:11:38,057 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:38,059 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:38,060 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:38,064 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:38,069 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:38,071 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:38,072 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:11:38,073 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:38,073 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:38,073 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:38,089 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,134 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,166 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,210 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,247 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,291 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,337 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,378 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,419 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,470 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,490 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {}, pending_retry_player: None
2025-07-15 23:11:38,492 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-15 23:11:38,506 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,541 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,590 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,625 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,669 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,713 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,757 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,796 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,831 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,867 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,904 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,942 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(0) < 目标(6)，添加虚拟玩家。
2025-07-15 23:11:38,944 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:38,945 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['东北大碴子(0次)', '柚稚(0次)', '山外山(0次)', '粉诱(0次)', '萌新小白(0次)']。已选择: 东北大碴子
2025-07-15 23:11:38,994 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 东北大碴子 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 23:11:39,012 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {}, pending_retry_player: None
2025-07-15 23:11:39,014 - [GameProcessThread] - INFO - [游戏线程] 玩家 东北大碴子(VirtualPlayerID0170) 开始免费游戏，扣除免费次数 (1)
2025-07-15 23:11:39,016 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 东北大碴子(VirtualPlayerID0170) 确保抓中, z_offset_extra=0
2025-07-15 23:11:39,016 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=738b91a2-2ae7-44cf-bba4-5d12f9d14ce2, Player=东北大碴子, Target=4, Z_Offset_Extra=0.0
2025-07-15 23:11:39,016 - [GameProcessThread] - INFO - [游戏线程] 玩家 东北大碴子(VirtualPlayerID0170) 抓取指令已发送到移动服务，命令ID: 738b91a2-2ae7-44cf-bba4-5d12f9d14ce2
2025-07-15 23:11:39,035 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,064 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2', 'status': 'queued', 'message': '抓取物体 4 指令已加入队列'}
2025-07-15 23:11:39,066 - [MoveServiceEventReceiver] - INFO - 抓取指令 738b91a2-2ae7-44cf-bba4-5d12f9d14ce2 已被移动服务接受并加入队列
2025-07-15 23:11:39,081 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,094 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:39,097 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:39,098 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:39,099 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:39,101 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:39,104 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:39,105 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 3
2025-07-15 23:11:39,107 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:39,108 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:39,119 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:39,122 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:39,123 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:39,124 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592299.124186
2025-07-15 23:11:39,124 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:39,127 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,125 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:39,132 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:39,135 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:39,138 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:39,149 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:39,151 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:11:39,152 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:39,158 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:39,158 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:39,176 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,224 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,270 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,317 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,363 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,409 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,457 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0170 订单状态: {'free_games_used_this_session': 1}
2025-07-15 23:11:39,460 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,465 - [DBSyncThread] - WARNING - 尝试更新玩家 VirtualPlayerID0170 订单状态，但该玩家在会话 1 中不存在
2025-07-15 23:11:39,505 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,535 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': 1752592299.0145736, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2'}, pending_retry_player: None
2025-07-15 23:11:39,537 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-15 23:11:39,552 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,597 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,648 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,693 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,739 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,785 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,834 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,880 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,925 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:39,966 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,012 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,042 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': 1752592299.0145736, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2'}, pending_retry_player: None
2025-07-15 23:11:40,044 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-15 23:11:40,057 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,107 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,153 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,169 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:40,169 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:40,170 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:40,170 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:40,171 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:40,171 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:40,172 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 4
2025-07-15 23:11:40,172 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:40,172 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:40,177 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:40,180 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:40,181 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:40,181 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592300.1815388
2025-07-15 23:11:40,182 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:40,182 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:40,183 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:40,183 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:40,188 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:40,192 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:40,193 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:11:40,193 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:40,194 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:40,195 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:40,202 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,248 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,297 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,344 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,385 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,433 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,481 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,528 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,557 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 0, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': 1752592299.0145736, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2'}, pending_retry_player: None
2025-07-15 23:11:40,557 - [GameProcessThread] - DEBUG - [游戏线程] 当前队列为空
2025-07-15 23:11:40,567 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,621 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,656 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,704 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,742 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,785 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,835 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,873 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,923 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:40,965 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:41,008 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 东北大碴子 已完成游戏，总游戏次数: 1
2025-07-15 23:11:41,013 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(0) < 目标(6)，添加虚拟玩家。
2025-07-15 23:11:41,014 - [DetectionReceiver] - ERROR - 未能生成队列快照，未发送更新事件 (因物体映射表变化)
2025-07-15 23:11:41,015 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['柚稚(0次)', '山外山(0次)', '粉诱(0次)', '萌新小白(0次)', '东北大碴子(1次)']。已选择: 柚稚
2025-07-15 23:11:41,019 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 柚稚 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 23:11:41,061 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': 1752592299.0145736, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2'}, pending_retry_player: None
2025-07-15 23:11:41,064 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:41,208 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:41,220 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:41,221 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:41,222 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:41,222 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:41,223 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:41,223 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 5
2025-07-15 23:11:41,226 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:41,226 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:41,226 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:41,226 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:41,226 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:41,226 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592301.226562
2025-07-15 23:11:41,226 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:41,226 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:41,226 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:41,226 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:41,226 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:41,242 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:41,249 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:11:41,250 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:41,251 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:41,254 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:41,570 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': 1752592299.0145736, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2'}, pending_retry_player: None
2025-07-15 23:11:41,572 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:41,855 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '4', 'message': '物品_4'}}
2025-07-15 23:11:41,855 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=738b91a2-2ae7-44cf-bba4-5d12f9d14ce2
2025-07-15 23:11:41,871 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '4', 'message': '物品_4'}
2025-07-15 23:11:41,873 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0170, player_name=东北大碴子, requested_object_id=4
2025-07-15 23:11:41,873 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 4, 物品名称: '物品_4', 原始消息: '物品_4'
2025-07-15 23:11:41,874 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2', 'player_id': 'VirtualPlayerID0170', 'player_name': '东北大碴子', 'item_name': '物品_4', 'success': True, 'object_id': '4', 'message': '物品_4', 'source': 'real_mode'}
2025-07-15 23:11:41,874 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 东北大碴子(VirtualPlayerID0170), 成功: True, 物品: 物品_4, 物品ID: 4, 来源: real_mode, ReqID: 738b91a2-2ae7-44cf-bba4-5d12f9d14ce2, 消息: 物品_4
2025-07-15 23:11:41,874 - [StatusUpdateThread] - INFO - 玩家 东北大碴子 本次是第 1 次成功抓取。
2025-07-15 23:11:41,874 - [StatusUpdateThread] - INFO - 为玩家 东北大碴子 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-15 23:11:41,874 - [StatusUpdateThread] - INFO - 显示祝贺特效视频：玩家=东北大碴子, 物品=物品_4
2025-07-15 23:11:41,874 - [StatusUpdateThread] - INFO - 正在连接到OBS WebSocket (host=localhost, port=4455)...
2025-07-15 23:11:41,874 - [StatusUpdateThread] - INFO - Connecting with parameters: host='localhost' port=4455 password='' subs=0 timeout=5
2025-07-15 23:11:42,086 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': 1752592299.0145736, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2'}, pending_retry_player: None
2025-07-15 23:11:42,087 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:42,269 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:42,269 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:42,269 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:42,269 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:42,269 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:42,269 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:42,269 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 6
2025-07-15 23:11:42,269 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:42,269 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:42,298 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:42,298 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:42,298 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:42,298 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592302.2984793
2025-07-15 23:11:42,298 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:42,305 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:42,305 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:42,305 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:42,306 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:42,307 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:42,307 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 23:11:42,308 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:42,308 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:42,308 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:42,596 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 1, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': 1752592299.0145736, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2'}, pending_retry_player: None
2025-07-15 23:11:42,596 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:43,041 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(1) < 目标(3)，添加虚拟玩家。
2025-07-15 23:11:43,043 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['山外山(0次)', '粉诱(0次)', '萌新小白(0次)', '东北大碴子(1次)']。已选择: 山外山
2025-07-15 23:11:43,044 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 山外山 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 23:11:43,110 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': 1752592299.0145736, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2'}, pending_retry_player: None
2025-07-15 23:11:43,115 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:43,325 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:43,327 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:43,327 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:43,327 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:43,327 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:43,327 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:43,327 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 7
2025-07-15 23:11:43,327 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:43,327 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:43,327 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:43,327 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:43,327 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:43,351 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592303.351536
2025-07-15 23:11:43,351 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:43,351 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:43,351 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:43,351 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:43,368 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:43,371 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:43,371 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:11:43,371 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:43,371 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:43,371 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:43,628 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': 1752592299.0145736, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2'}, pending_retry_player: None
2025-07-15 23:11:43,637 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:44,154 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': 1752592299.0145736, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2'}, pending_retry_player: None
2025-07-15 23:11:44,154 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:44,381 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:44,383 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:44,384 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:44,384 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:44,384 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:44,384 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:44,384 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 8
2025-07-15 23:11:44,385 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:44,385 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:44,389 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:44,391 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:44,392 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:44,392 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592304.3920214
2025-07-15 23:11:44,393 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:44,393 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:44,393 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:44,393 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:44,394 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:44,395 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:44,395 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:11:44,395 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:44,396 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:44,396 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:44,668 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': 1752592299.0145736, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2'}, pending_retry_player: None
2025-07-15 23:11:44,673 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:45,057 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(2) < 目标(7)，添加虚拟玩家。
2025-07-15 23:11:45,061 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['粉诱(0次)', '萌新小白(0次)', '东北大碴子(1次)']。已选择: 粉诱
2025-07-15 23:11:45,063 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 粉诱 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 23:11:45,183 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': 1752592299.0145736, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2'}, pending_retry_player: None
2025-07-15 23:11:45,183 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:45,407 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:45,410 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:45,413 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:45,414 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:45,415 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:45,416 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:45,417 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 9
2025-07-15 23:11:45,417 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:45,418 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:45,422 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:45,429 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:45,430 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:45,430 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592305.4307482
2025-07-15 23:11:45,430 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:45,431 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:45,431 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:45,431 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:45,432 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:45,433 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:45,434 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 23:11:45,434 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:45,434 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:45,434 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:45,704 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': 1752592299.0145736, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2'}, pending_retry_player: None
2025-07-15 23:11:45,706 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:45,953 - [StatusUpdateThread] - ERROR - ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Program Files\Python39\lib\site-packages\obsws_python\baseclient.py", line 41, in __init__
    self.ws.connect(f"ws://{self.host}:{self.port}", timeout=self.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_core.py", line 256, in connect
    self.sock, addrs = connect(
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 145, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 232, in _open_socket
    raise err
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 209, in _open_socket
    sock.connect(address)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-15 23:11:45,953 - [StatusUpdateThread] - ERROR - 连接到OBS WebSocket失败: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-15 23:11:45,980 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '738b91a2-2ae7-44cf-bba4-5d12f9d14ce2', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-15 23:11:45,980 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=738b91a2-2ae7-44cf-bba4-5d12f9d14ce2
2025-07-15 23:11:45,980 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-15 23:11:45,980 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0170, player_name=东北大碴子, requested_object_id=4
2025-07-15 23:11:45,980 - [MoveServiceEventReceiver] - INFO - 移动服务请求 738b91a2-2ae7-44cf-bba4-5d12f9d14ce2 (cycle_completed) 已完成，从活动命令中移除。
2025-07-15 23:11:46,104 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 东北大碴子(VirtualPlayerID0170)
2025-07-15 23:11:46,104 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_4'，准备结束游戏。
2025-07-15 23:11:46,106 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 东北大碴子(VirtualPlayerID0170), 结果: 物品_4, 订单: None
2025-07-15 23:11:46,107 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-15 23:11:46,107 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-15 23:11:46,219 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {}, pending_retry_player: None
2025-07-15 23:11:46,225 - [GameProcessThread] - INFO - [游戏线程] 玩家 柚稚(VirtualPlayerID0171) 开始免费游戏，扣除免费次数 (1)
2025-07-15 23:11:46,228 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 柚稚(VirtualPlayerID0171) 确保抓中, z_offset_extra=0
2025-07-15 23:11:46,231 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=6e0668b0-6c7c-4973-be86-59208294ef77, Player=柚稚, Target=3, Z_Offset_Extra=0.0
2025-07-15 23:11:46,233 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '6e0668b0-6c7c-4973-be86-59208294ef77', 'status': 'queued', 'message': '抓取物体 3 指令已加入队列'}
2025-07-15 23:11:46,234 - [GameProcessThread] - INFO - [游戏线程] 玩家 柚稚(VirtualPlayerID0171) 抓取指令已发送到移动服务，命令ID: 6e0668b0-6c7c-4973-be86-59208294ef77
2025-07-15 23:11:46,237 - [MoveServiceEventReceiver] - INFO - 抓取指令 6e0668b0-6c7c-4973-be86-59208294ef77 已被移动服务接受并加入队列
2025-07-15 23:11:46,449 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:46,453 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:46,453 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:46,454 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:46,454 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:46,455 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:46,456 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 10
2025-07-15 23:11:46,457 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:46,457 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:46,462 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:46,469 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:46,470 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:46,470 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592306.4705946
2025-07-15 23:11:46,470 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:46,471 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:46,471 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:46,471 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:46,472 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:46,472 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:46,473 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 23:11:46,473 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:46,473 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:46,474 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:46,615 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.028秒 - 玩家: VirtualPlayerID0170, 结果: 物品_4, 订单: NULL
2025-07-15 23:11:46,617 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0170] = 1
2025-07-15 23:11:46,627 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0171 订单状态: {'free_games_used_this_session': 1}
2025-07-15 23:11:46,632 - [DBSyncThread] - WARNING - 尝试更新玩家 VirtualPlayerID0171 订单状态，但该玩家在会话 1 中不存在
2025-07-15 23:11:46,740 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}, pending_retry_player: None
2025-07-15 23:11:46,740 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:47,075 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 柚稚 已完成游戏，总游戏次数: 1
2025-07-15 23:11:47,075 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(2) < 目标(7)，添加虚拟玩家。
2025-07-15 23:11:47,077 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['萌新小白(0次)', '东北大碴子(1次)', '柚稚(1次)']。已选择: 萌新小白
2025-07-15 23:11:47,078 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 萌新小白 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 23:11:47,246 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}, pending_retry_player: None
2025-07-15 23:11:47,248 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:47,275 - [DBSyncThread] - INFO - 数据库操作: 更新队列完成，耗时 0.029秒
2025-07-15 23:11:47,276 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-15 23:11:47,299 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 5 个玩家, 成功更新 5 条记录.
2025-07-15 23:11:47,482 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:47,488 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:47,492 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:47,494 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:47,494 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:47,495 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:47,495 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 11
2025-07-15 23:11:47,495 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:47,496 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:47,500 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:47,504 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:47,505 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:47,506 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592307.5064063
2025-07-15 23:11:47,506 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:47,507 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:47,507 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:47,507 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:47,507 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:47,528 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:47,528 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 23:11:47,528 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:47,529 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:47,529 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:47,632 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '6e0668b0-6c7c-4973-be86-59208294ef77', 'event_name': 'hardware_error', 'data': {'error_message': '检测到电机硬件故障，已终止本轮操作', 'faults': [{'axis': 'motor_x', 'fault_type': 'hardware_fault', 'fault_msg': '模拟硬件故障（测试）'}, {'axis': 'motor_z', 'fault_type': 'timeout_fault', 'fault_msg': '模拟超时故障（测试）'}], 'player_id': 'VirtualPlayerID0171', 'player_name': '柚稚'}}
2025-07-15 23:11:47,633 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='hardware_error', request_id=6e0668b0-6c7c-4973-be86-59208294ef77
2025-07-15 23:11:47,634 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'error_message': '检测到电机硬件故障，已终止本轮操作', 'faults': [{'axis': 'motor_x', 'fault_type': 'hardware_fault', 'fault_msg': '模拟硬件故障（测试）'}, {'axis': 'motor_z', 'fault_type': 'timeout_fault', 'fault_msg': '模拟超时故障（测试）'}], 'player_id': 'VirtualPlayerID0171', 'player_name': '柚稚'}
2025-07-15 23:11:47,635 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0171, player_name=柚稚, requested_object_id=3
2025-07-15 23:11:47,635 - [MoveServiceEventReceiver] - INFO - [事件转换] 处理其他运动模块事件: 'hardware_error'
2025-07-15 23:11:47,636 - [MoveServiceEventReceiver] - DEBUG - [事件发送] 通用移动服务事件 'hardware_error' 已发送: {'type': 'move_service_event', 'request_id': '6e0668b0-6c7c-4973-be86-59208294ef77', 'player_id': 'VirtualPlayerID0171', 'player_name': '柚稚', 'requested_object_id': '3', 'event_name': 'hardware_error', 'data': {'error_message': '检测到电机硬件故障，已终止本轮操作', 'faults': [{'axis': 'motor_x', 'fault_type': 'hardware_fault', 'fault_msg': '模拟硬件故障（测试）'}, {'axis': 'motor_z', 'fault_type': 'timeout_fault', 'fault_msg': '模拟超时故障（测试）'}], 'player_id': 'VirtualPlayerID0171', 'player_name': '柚稚'}}
2025-07-15 23:11:47,636 - [StatusUpdateThread] - ERROR - [移动服务端故障] hardware_error for 柚稚(VirtualPlayerID0171), request_id=6e0668b0-6c7c-4973-be86-59208294ef77, data={"error_message": "检测到电机硬件故障，已终止本轮操作", "faults": [{"axis": "motor_x", "fault_type": "hardware_fault", "fault_msg": "模拟硬件故障（测试）"}, {"axis": "motor_z", "fault_type": "timeout_fault", "fault_msg": "模拟超时故障（测试）"}], "player_id": "VirtualPlayerID0171", "player_name": "柚稚"}
2025-07-15 23:11:47,636 - [StatusUpdateThread] - ERROR - [硬件故障详情] axis=motor_x, fault_type=hardware_fault, fault_msg=模拟硬件故障（测试）
2025-07-15 23:11:47,637 - [StatusUpdateThread] - ERROR - [硬件故障详情] axis=motor_z, fault_type=timeout_fault, fault_msg=模拟超时故障（测试）
2025-07-15 23:11:47,637 - [StatusUpdateThread] - INFO - [重试机制] 由于hardware_error，移动服务设为未就绪状态
2025-07-15 23:11:47,637 - [StatusUpdateThread] - WARNING - [重试机制] 玩家 柚稚(VirtualPlayerID0171) 已进入pending_retry_player，等待移动服务端恢复后重试。
2025-07-15 23:11:47,752 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:47,752 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:47,755 - [GameProcessThread] - INFO - [游戏线程] 玩家 山外山(VirtualPlayerID0172) 开始免费游戏，扣除免费次数 (1)
2025-07-15 23:11:47,755 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 山外山(VirtualPlayerID0172) 确保抓中, z_offset_extra=0
2025-07-15 23:11:47,757 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=613aa4ac-4b2d-40c7-9e99-97a4c849a545, Player=山外山, Target=3, Z_Offset_Extra=0.0
2025-07-15 23:11:47,757 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545', 'status': 'queued', 'message': '抓取物体 3 指令已加入队列'}
2025-07-15 23:11:47,758 - [GameProcessThread] - INFO - [游戏线程] 玩家 山外山(VirtualPlayerID0172) 抓取指令已发送到移动服务，命令ID: 613aa4ac-4b2d-40c7-9e99-97a4c849a545
2025-07-15 23:11:47,758 - [MoveServiceEventReceiver] - INFO - 抓取指令 613aa4ac-4b2d-40c7-9e99-97a4c849a545 已被移动服务接受并加入队列
2025-07-15 23:11:47,809 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0172 订单状态: {'free_games_used_this_session': 1}
2025-07-15 23:11:47,825 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0172 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-15 23:11:48,266 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0172', 'name': '山外山', 'start_time': 1752592307.7556283, 'target_id': '3', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545'}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:48,266 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:48,269 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:48,536 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:48,539 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:48,540 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:48,541 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:48,542 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:48,543 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:48,544 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 12
2025-07-15 23:11:48,544 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:48,545 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:48,548 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:48,569 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:48,573 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:48,576 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592308.5762694
2025-07-15 23:11:48,576 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:48,577 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:48,578 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:48,579 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:48,586 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:48,588 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:48,588 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 23:11:48,589 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:48,590 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:48,592 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:48,773 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 2, current_player: {'player_id': 'VirtualPlayerID0172', 'name': '山外山', 'start_time': 1752592307.7556283, 'target_id': '3', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545'}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:48,775 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:48,776 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:49,082 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 山外山 已完成游戏，总游戏次数: 1
2025-07-15 23:11:49,082 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(2) < 目标(3)，添加虚拟玩家。
2025-07-15 23:11:49,084 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['东北大碴子(1次)', '柚稚(1次)', '山外山(1次)']。已选择: 东北大碴子
2025-07-15 23:11:49,084 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 东北大碴子 已加入队列末尾。优先级: 4.71 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 23:11:49,278 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0172', 'name': '山外山', 'start_time': 1752592307.7556283, 'target_id': '3', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545'}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:49,278 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:49,278 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:49,602 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:49,604 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:49,605 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:49,606 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:49,607 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:49,607 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:49,607 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 13
2025-07-15 23:11:49,608 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:49,608 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:49,609 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:49,628 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:49,630 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:49,630 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592309.6302826
2025-07-15 23:11:49,630 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:49,634 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:49,634 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:49,635 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:49,635 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:49,636 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:49,636 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:11:49,637 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:49,638 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:49,638 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:49,802 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0172', 'name': '山外山', 'start_time': 1752592307.7556283, 'target_id': '3', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545'}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:49,803 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:49,806 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:50,316 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0172', 'name': '山外山', 'start_time': 1752592307.7556283, 'target_id': '3', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545'}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:50,316 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:50,316 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:50,617 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '3', 'message': '物品_3'}}
2025-07-15 23:11:50,618 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=613aa4ac-4b2d-40c7-9e99-97a4c849a545
2025-07-15 23:11:50,620 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '3', 'message': '物品_3'}
2025-07-15 23:11:50,621 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0172, player_name=山外山, requested_object_id=3
2025-07-15 23:11:50,621 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 3, 物品名称: '物品_3', 原始消息: '物品_3'
2025-07-15 23:11:50,622 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545', 'player_id': 'VirtualPlayerID0172', 'player_name': '山外山', 'item_name': '物品_3', 'success': True, 'object_id': '3', 'message': '物品_3', 'source': 'real_mode'}
2025-07-15 23:11:50,622 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 山外山(VirtualPlayerID0172), 成功: True, 物品: 物品_3, 物品ID: 3, 来源: real_mode, ReqID: 613aa4ac-4b2d-40c7-9e99-97a4c849a545, 消息: 物品_3
2025-07-15 23:11:50,623 - [StatusUpdateThread] - INFO - 玩家 山外山 本次是第 1 次成功抓取。
2025-07-15 23:11:50,623 - [StatusUpdateThread] - INFO - 为玩家 山外山 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-15 23:11:50,623 - [StatusUpdateThread] - INFO - 显示祝贺特效视频：玩家=山外山, 物品=物品_3
2025-07-15 23:11:50,623 - [StatusUpdateThread] - INFO - 正在连接到OBS WebSocket (host=localhost, port=4455)...
2025-07-15 23:11:50,623 - [StatusUpdateThread] - INFO - Connecting with parameters: host='localhost' port=4455 password='' subs=0 timeout=5
2025-07-15 23:11:50,644 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:50,646 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:50,646 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:50,646 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:50,646 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:50,646 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:50,646 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 14
2025-07-15 23:11:50,646 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:50,646 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:50,668 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:50,673 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:50,674 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:50,675 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592310.6756575
2025-07-15 23:11:50,675 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:50,676 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:50,676 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:50,677 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:50,679 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:50,679 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:50,680 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 23:11:50,680 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:50,681 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:50,681 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:50,827 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 3, current_player: {'player_id': 'VirtualPlayerID0172', 'name': '山外山', 'start_time': 1752592307.7556283, 'target_id': '3', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545'}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:50,828 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:50,830 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:51,094 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(3) < 目标(7)，添加虚拟玩家。
2025-07-15 23:11:51,094 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['柚稚(1次)', '山外山(1次)']。已选择: 柚稚
2025-07-15 23:11:51,097 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 柚稚 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 23:11:51,341 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0172', 'name': '山外山', 'start_time': 1752592307.7556283, 'target_id': '3', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545'}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:51,341 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:51,341 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:51,691 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:51,691 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:51,691 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:51,691 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:51,691 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:51,691 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:51,691 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 15
2025-07-15 23:11:51,691 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:51,691 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:51,701 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:51,721 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:51,732 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:51,732 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592311.7321963
2025-07-15 23:11:51,733 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:51,733 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:51,734 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:51,734 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:51,734 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:51,734 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:51,734 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 23:11:51,734 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:51,734 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:51,734 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:51,858 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0172', 'name': '山外山', 'start_time': 1752592307.7556283, 'target_id': '3', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545'}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:51,859 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:51,860 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:52,378 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0172', 'name': '山外山', 'start_time': 1752592307.7556283, 'target_id': '3', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545'}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:52,380 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:52,383 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:52,750 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:52,758 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:52,760 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:52,760 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:52,760 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:52,760 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:52,760 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 16
2025-07-15 23:11:52,760 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:52,760 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:52,775 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:52,775 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:52,775 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:52,775 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592312.7758212
2025-07-15 23:11:52,775 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:52,775 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:52,775 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:52,775 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:52,775 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:52,775 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:52,775 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:11:52,775 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:52,775 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:52,775 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:52,902 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0172', 'name': '山外山', 'start_time': 1752592307.7556283, 'target_id': '3', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545'}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:52,906 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:52,907 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:53,103 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(4) < 目标(5)，添加虚拟玩家。
2025-07-15 23:11:53,106 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['山外山(1次)']。已选择: 山外山
2025-07-15 23:11:53,109 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 山外山 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 23:11:53,413 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0172', 'name': '山外山', 'start_time': 1752592307.7556283, 'target_id': '3', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545'}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:53,413 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:53,413 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:53,790 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:53,794 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:53,795 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:53,798 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:53,800 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:53,800 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:53,801 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 17
2025-07-15 23:11:53,801 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:53,802 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:53,808 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:53,813 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:53,819 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:53,821 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592313.82114
2025-07-15 23:11:53,823 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:53,824 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:53,825 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:53,826 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:53,833 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:53,838 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:53,839 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 23:11:53,840 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:53,841 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:53,843 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:53,929 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0172', 'name': '山外山', 'start_time': 1752592307.7556283, 'target_id': '3', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545'}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:53,932 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:53,934 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:54,440 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0172', 'name': '山外山', 'start_time': 1752592307.7556283, 'target_id': '3', 'target_object': '机器人模型', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545'}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:54,442 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:54,445 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:54,708 - [StatusUpdateThread] - ERROR - ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Program Files\Python39\lib\site-packages\obsws_python\baseclient.py", line 41, in __init__
    self.ws.connect(f"ws://{self.host}:{self.port}", timeout=self.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_core.py", line 256, in connect
    self.sock, addrs = connect(
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 145, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 232, in _open_socket
    raise err
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 209, in _open_socket
    sock.connect(address)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-15 23:11:54,718 - [StatusUpdateThread] - ERROR - 连接到OBS WebSocket失败: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-15 23:11:54,859 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:54,871 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '613aa4ac-4b2d-40c7-9e99-97a4c849a545', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-15 23:11:54,866 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:54,877 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=613aa4ac-4b2d-40c7-9e99-97a4c849a545
2025-07-15 23:11:54,878 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:54,878 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-15 23:11:54,878 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:54,878 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0172, player_name=山外山, requested_object_id=3
2025-07-15 23:11:54,878 - [MoveServiceEventReceiver] - INFO - 移动服务请求 613aa4ac-4b2d-40c7-9e99-97a4c849a545 (cycle_completed) 已完成，从活动命令中移除。
2025-07-15 23:11:54,878 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:54,884 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:54,884 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 18
2025-07-15 23:11:54,885 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:54,886 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:54,890 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:54,892 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:54,894 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:54,901 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: **********.901743
2025-07-15 23:11:54,903 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:54,907 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:54,909 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:54,922 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:54,928 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:54,928 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:54,928 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:11:54,928 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:54,928 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:54,933 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:54,935 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 山外山(VirtualPlayerID0172)
2025-07-15 23:11:54,935 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_3'，准备结束游戏。
2025-07-15 23:11:54,935 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 山外山(VirtualPlayerID0172), 结果: 物品_3, 订单: None
2025-07-15 23:11:54,935 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-15 23:11:54,935 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-15 23:11:54,967 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:54,970 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:54,973 - [GameProcessThread] - INFO - [游戏线程] 玩家 粉诱(VirtualPlayerID0173) 开始免费游戏，扣除免费次数 (1)
2025-07-15 23:11:54,973 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 粉诱(VirtualPlayerID0173) 确保抓中, z_offset_extra=0
2025-07-15 23:11:54,973 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=af195e02-dd3b-43a5-81a1-9ebe66b4a139, Player=粉诱, Target=4, Z_Offset_Extra=0.0
2025-07-15 23:11:54,988 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139', 'status': 'queued', 'message': '抓取物体 4 指令已加入队列'}
2025-07-15 23:11:54,998 - [GameProcessThread] - INFO - [游戏线程] 玩家 粉诱(VirtualPlayerID0173) 抓取指令已发送到移动服务，命令ID: af195e02-dd3b-43a5-81a1-9ebe66b4a139
2025-07-15 23:11:54,999 - [MoveServiceEventReceiver] - INFO - 抓取指令 af195e02-dd3b-43a5-81a1-9ebe66b4a139 已被移动服务接受并加入队列
2025-07-15 23:11:55,000 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.039秒 - 玩家: VirtualPlayerID0172, 结果: 物品_3, 订单: NULL
2025-07-15 23:11:55,000 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0172] = 1
2025-07-15 23:11:55,029 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0173 订单状态: {'free_games_used_this_session': 1}
2025-07-15 23:11:55,041 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0173 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-15 23:11:55,120 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 粉诱 已完成游戏，总游戏次数: 1
2025-07-15 23:11:55,123 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(4) < 目标(7)，添加虚拟玩家。
2025-07-15 23:11:55,128 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['粉诱(1次)']。已选择: 粉诱
2025-07-15 23:11:55,132 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 粉诱 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 23:11:55,501 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:55,501 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:55,501 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:55,946 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:55,950 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:55,950 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:55,951 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:55,952 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:55,952 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:55,953 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 19
2025-07-15 23:11:55,953 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:55,953 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:55,958 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:55,980 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:55,984 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:55,985 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592315.985584
2025-07-15 23:11:55,986 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:55,988 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:55,989 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:55,990 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:55,994 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:55,997 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:55,998 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:11:55,998 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:55,999 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:56,004 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:56,023 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:56,023 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:56,025 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:56,533 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}, pending_retry_player: {'entry_tuple': (['', 1752592306.22535, '柚稚', 'VirtualPlayerID0171', '3', '', '皮卡丘'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0171', 'name': '柚稚', 'start_time': 1752592306.22535, 'target_id': '3', 'target_object': '皮卡丘', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '6e0668b0-6c7c-4973-be86-59208294ef77'}}
2025-07-15 23:11:56,535 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:56,535 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:56,588 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139', 'event_name': 'hardware_error', 'data': {'error_message': '检测到电机硬件故障，已终止本轮操作', 'faults': [{'axis': 'motor_x', 'fault_type': 'hardware_fault', 'fault_msg': '模拟硬件故障（测试）'}, {'axis': 'motor_z', 'fault_type': 'timeout_fault', 'fault_msg': '模拟超时故障（测试）'}], 'player_id': 'VirtualPlayerID0173', 'player_name': '粉诱'}}
2025-07-15 23:11:56,588 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='hardware_error', request_id=af195e02-dd3b-43a5-81a1-9ebe66b4a139
2025-07-15 23:11:56,595 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'error_message': '检测到电机硬件故障，已终止本轮操作', 'faults': [{'axis': 'motor_x', 'fault_type': 'hardware_fault', 'fault_msg': '模拟硬件故障（测试）'}, {'axis': 'motor_z', 'fault_type': 'timeout_fault', 'fault_msg': '模拟超时故障（测试）'}], 'player_id': 'VirtualPlayerID0173', 'player_name': '粉诱'}
2025-07-15 23:11:56,597 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0173, player_name=粉诱, requested_object_id=4
2025-07-15 23:11:56,597 - [MoveServiceEventReceiver] - INFO - [事件转换] 处理其他运动模块事件: 'hardware_error'
2025-07-15 23:11:56,598 - [MoveServiceEventReceiver] - DEBUG - [事件发送] 通用移动服务事件 'hardware_error' 已发送: {'type': 'move_service_event', 'request_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139', 'player_id': 'VirtualPlayerID0173', 'player_name': '粉诱', 'requested_object_id': '4', 'event_name': 'hardware_error', 'data': {'error_message': '检测到电机硬件故障，已终止本轮操作', 'faults': [{'axis': 'motor_x', 'fault_type': 'hardware_fault', 'fault_msg': '模拟硬件故障（测试）'}, {'axis': 'motor_z', 'fault_type': 'timeout_fault', 'fault_msg': '模拟超时故障（测试）'}], 'player_id': 'VirtualPlayerID0173', 'player_name': '粉诱'}}
2025-07-15 23:11:56,599 - [StatusUpdateThread] - ERROR - [移动服务端故障] hardware_error for 粉诱(VirtualPlayerID0173), request_id=af195e02-dd3b-43a5-81a1-9ebe66b4a139, data={"error_message": "检测到电机硬件故障，已终止本轮操作", "faults": [{"axis": "motor_x", "fault_type": "hardware_fault", "fault_msg": "模拟硬件故障（测试）"}, {"axis": "motor_z", "fault_type": "timeout_fault", "fault_msg": "模拟超时故障（测试）"}], "player_id": "VirtualPlayerID0173", "player_name": "粉诱"}
2025-07-15 23:11:56,600 - [StatusUpdateThread] - ERROR - [硬件故障详情] axis=motor_x, fault_type=hardware_fault, fault_msg=模拟硬件故障（测试）
2025-07-15 23:11:56,601 - [StatusUpdateThread] - ERROR - [硬件故障详情] axis=motor_z, fault_type=timeout_fault, fault_msg=模拟超时故障（测试）
2025-07-15 23:11:56,602 - [StatusUpdateThread] - INFO - [重试机制] 由于hardware_error，移动服务设为未就绪状态
2025-07-15 23:11:56,602 - [StatusUpdateThread] - WARNING - [重试机制] 玩家 粉诱(VirtualPlayerID0173) 已进入pending_retry_player，等待移动服务端恢复后重试。
2025-07-15 23:11:57,018 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:57,026 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:57,031 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:57,033 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:57,036 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:57,036 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:57,037 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:11:57,037 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 20
2025-07-15 23:11:57,037 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:57,037 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:57,037 - [GameProcessThread] - INFO - [游戏线程] 玩家 萌新小白(VirtualPlayerID0174) 开始免费游戏，扣除免费次数 (1)
2025-07-15 23:11:57,037 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:57,037 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 萌新小白(VirtualPlayerID0174) 确保抓中, z_offset_extra=0
2025-07-15 23:11:57,037 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=3e108c07-b2e0-4a08-9f0a-cb05f124c070, Player=萌新小白, Target=5, Z_Offset_Extra=0.0
2025-07-15 23:11:57,037 - [GameProcessThread] - INFO - [游戏线程] 玩家 萌新小白(VirtualPlayerID0174) 抓取指令已发送到移动服务，命令ID: 3e108c07-b2e0-4a08-9f0a-cb05f124c070
2025-07-15 23:11:57,037 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070', 'status': 'queued', 'message': '抓取物体 5 指令已加入队列'}
2025-07-15 23:11:57,037 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:57,037 - [MoveServiceEventReceiver] - INFO - 抓取指令 3e108c07-b2e0-4a08-9f0a-cb05f124c070 已被移动服务接受并加入队列
2025-07-15 23:11:57,037 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:57,037 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:57,037 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592317.0379202
2025-07-15 23:11:57,037 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:57,037 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:57,037 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:57,037 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:57,037 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:57,037 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:57,037 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:11:57,037 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:57,037 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:57,051 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:57,083 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0174 订单状态: {'free_games_used_this_session': 1}
2025-07-15 23:11:57,094 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0174 的订单相关状态: {'free_games_used_this_session': 1}
2025-07-15 23:11:57,140 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 萌新小白 已完成游戏，总游戏次数: 1
2025-07-15 23:11:57,552 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0174', 'name': '萌新小白', 'start_time': 1752592317.0379202, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:11:57,558 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:57,559 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:57,629 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-15 23:11:57,670 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 5 个玩家, 成功更新 5 条记录.
2025-07-15 23:11:58,069 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:58,069 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0174', 'name': '萌新小白', 'start_time': 1752592317.0379202, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:11:58,071 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:58,071 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:58,071 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:58,072 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:58,072 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:58,072 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:58,072 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:58,073 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 21
2025-07-15 23:11:58,073 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:58,073 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:58,075 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:58,076 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:58,077 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:58,077 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592318.0778112
2025-07-15 23:11:58,078 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:58,078 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:58,078 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:58,078 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:58,080 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:58,080 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:58,080 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 23:11:58,081 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:58,081 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:58,082 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:58,580 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0174', 'name': '萌新小白', 'start_time': 1752592317.0379202, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:11:58,580 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:58,580 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:59,098 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:11:59,100 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0174', 'name': '萌新小白', 'start_time': 1752592317.0379202, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:11:59,100 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:11:59,100 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:59,100 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:11:59,100 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:59,100 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:11:59,100 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:11:59,124 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:11:59,128 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 22
2025-07-15 23:11:59,129 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:11:59,130 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:11:59,135 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:11:59,138 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:11:59,139 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:11:59,140 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592319.1406295
2025-07-15 23:11:59,140 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:11:59,141 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:11:59,141 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:11:59,141 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:11:59,142 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:59,143 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:11:59,144 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 23:11:59,144 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:11:59,145 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:11:59,150 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家-填充] 真实玩家数(0) <= 阈值(2)，且总数(4) < 目标(5)，添加虚拟玩家。
2025-07-15 23:11:59,146 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:11:59,162 - [VirtualPlayerManagerThread] - DEBUG - [虚拟玩家] 候选玩家池: ['萌新小白(1次)']。已选择: 萌新小白
2025-07-15 23:11:59,165 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 萌新小白 已加入队列末尾。优先级: 10.61 (此优先级仅供参考，位置由注入逻辑决定)
2025-07-15 23:11:59,622 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0174', 'name': '萌新小白', 'start_time': 1752592317.0379202, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:11:59,624 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:11:59,625 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:11:59,886 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070', 'event_name': 'object_picked', 'data': {'success': True, 'object_id': '5', 'message': '物品_5'}}
2025-07-15 23:11:59,888 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='object_picked', request_id=3e108c07-b2e0-4a08-9f0a-cb05f124c070
2025-07-15 23:11:59,893 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'success': True, 'object_id': '5', 'message': '物品_5'}
2025-07-15 23:11:59,895 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0174, player_name=萌新小白, requested_object_id=5
2025-07-15 23:11:59,896 - [MoveServiceEventReceiver] - INFO - [事件转换] 检测到object_picked事件，success: True, object_id: 5, 物品名称: '物品_5', 原始消息: '物品_5'
2025-07-15 23:11:59,897 - [MoveServiceEventReceiver] - INFO - [事件发送] 已转换并发送统一 'object_picked' 事件: {'type': 'object_picked', 'request_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070', 'player_id': 'VirtualPlayerID0174', 'player_name': '萌新小白', 'item_name': '物品_5', 'success': True, 'object_id': '5', 'message': '物品_5', 'source': 'real_mode'}
2025-07-15 23:11:59,898 - [StatusUpdateThread] - INFO - 统一事件 'object_picked': 玩家 萌新小白(VirtualPlayerID0174), 成功: True, 物品: 物品_5, 物品ID: 5, 来源: real_mode, ReqID: 3e108c07-b2e0-4a08-9f0a-cb05f124c070, 消息: 物品_5
2025-07-15 23:11:59,900 - [StatusUpdateThread] - INFO - 玩家 萌新小白 本次是第 1 次成功抓取。
2025-07-15 23:11:59,901 - [StatusUpdateThread] - INFO - 为玩家 萌新小白 生成优惠券提示: 获得满减20元券！
再抓到一次优惠翻倍！
2025-07-15 23:11:59,902 - [StatusUpdateThread] - INFO - 显示祝贺特效视频：玩家=萌新小白, 物品=物品_5
2025-07-15 23:11:59,902 - [StatusUpdateThread] - INFO - 正在连接到OBS WebSocket (host=localhost, port=4455)...
2025-07-15 23:11:59,903 - [StatusUpdateThread] - INFO - Connecting with parameters: host='localhost' port=4455 password='' subs=0 timeout=5
2025-07-15 23:12:00,142 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0174', 'name': '萌新小白', 'start_time': 1752592317.0379202, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:12:00,143 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:12:00,144 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:12:00,184 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:12:00,186 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:12:00,187 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:12:00,188 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:12:00,189 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:12:00,189 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:12:00,190 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 23
2025-07-15 23:12:00,190 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:12:00,190 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:12:00,190 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:12:00,198 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:12:00,199 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:12:00,199 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592320.1992626
2025-07-15 23:12:00,200 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:12:00,200 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:12:00,201 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:12:00,201 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:12:00,203 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:00,204 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:12:00,204 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:12:00,204 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:12:00,205 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:00,206 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:12:00,656 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0174', 'name': '萌新小白', 'start_time': 1752592317.0379202, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:12:00,656 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:12:00,656 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:12:01,176 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0174', 'name': '萌新小白', 'start_time': 1752592317.0379202, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:12:01,179 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:12:01,182 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:12:01,217 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:12:01,217 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:12:01,217 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:12:01,217 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:12:01,217 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:12:01,217 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:12:01,217 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 24
2025-07-15 23:12:01,217 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:12:01,217 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:12:01,233 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:12:01,248 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:12:01,264 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:12:01,264 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592321.2643096
2025-07-15 23:12:01,264 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:12:01,264 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:12:01,264 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:12:01,264 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:12:01,264 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:01,271 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:12:01,271 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 23:12:01,271 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:12:01,271 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:01,271 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:12:01,687 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0174', 'name': '萌新小白', 'start_time': 1752592317.0379202, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:12:01,691 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:12:01,693 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:12:02,205 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0174', 'name': '萌新小白', 'start_time': 1752592317.0379202, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:12:02,206 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:12:02,206 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:12:02,288 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:12:02,290 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:12:02,290 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:12:02,290 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:12:02,290 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:12:02,290 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:12:02,290 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 25
2025-07-15 23:12:02,290 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:12:02,290 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:12:02,298 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:12:02,299 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:12:02,299 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:12:02,299 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592322.2993188
2025-07-15 23:12:02,299 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:12:02,299 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:12:02,299 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:12:02,299 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:12:02,309 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:02,324 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:12:02,324 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:12:02,324 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:12:02,324 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:02,334 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:12:02,713 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0174', 'name': '萌新小白', 'start_time': 1752592317.0379202, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:12:02,713 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:12:02,713 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:12:03,221 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0174', 'name': '萌新小白', 'start_time': 1752592317.0379202, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:12:03,221 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:12:03,221 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:12:03,350 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:12:03,353 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:12:03,353 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:12:03,353 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:12:03,353 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:12:03,353 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:12:03,353 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 26
2025-07-15 23:12:03,353 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:12:03,353 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:12:03,378 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:12:03,403 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:12:03,408 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:12:03,410 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592323.410068
2025-07-15 23:12:03,411 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:12:03,411 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:12:03,411 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:12:03,411 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:12:03,411 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:03,411 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:12:03,427 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:12:03,432 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:12:03,432 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:03,434 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:12:03,747 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {'player_id': 'VirtualPlayerID0174', 'name': '萌新小白', 'start_time': 1752592317.0379202, 'target_id': '5', 'target_object': '泰迪熊', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:12:03,749 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:12:03,752 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:12:03,971 - [StatusUpdateThread] - ERROR - ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Program Files\Python39\lib\site-packages\obsws_python\baseclient.py", line 41, in __init__
    self.ws.connect(f"ws://{self.host}:{self.port}", timeout=self.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_core.py", line 256, in connect
    self.sock, addrs = connect(
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 145, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 232, in _open_socket
    raise err
  File "C:\Program Files\Python39\lib\site-packages\websocket\_http.py", line 209, in _open_socket
    sock.connect(address)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-15 23:12:03,973 - [StatusUpdateThread] - ERROR - 连接到OBS WebSocket失败: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-15 23:12:04,023 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'event', 'request_id': '3e108c07-b2e0-4a08-9f0a-cb05f124c070', 'event_name': 'cycle_completed', 'data': {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}}
2025-07-15 23:12:04,025 - [MoveServiceEventReceiver] - INFO - [事件接收] 收到运动模块事件: event_name='cycle_completed', request_id=3e108c07-b2e0-4a08-9f0a-cb05f124c070
2025-07-15 23:12:04,031 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 事件载荷: {'status': 'success', 'message': '抓取流程已结束，机械臂已返回起点', 'final_positions': {'X': 0.0, 'Y': 0.0, 'Z': 0.0, 'CAM': 0.0}}
2025-07-15 23:12:04,034 - [MoveServiceEventReceiver] - DEBUG - [事件接收] 玩家详情: player_id=VirtualPlayerID0174, player_name=萌新小白, requested_object_id=5
2025-07-15 23:12:04,035 - [MoveServiceEventReceiver] - INFO - 移动服务请求 3e108c07-b2e0-4a08-9f0a-cb05f124c070 (cycle_completed) 已完成，从活动命令中移除。
2025-07-15 23:12:04,131 - [StatusUpdateThread] - INFO - 移动服务事件 'cycle_completed' for player 萌新小白(VirtualPlayerID0174)
2025-07-15 23:12:04,131 - [StatusUpdateThread] - INFO - 从 current_player 获取到游戏结果: '物品_5'，准备结束游戏。
2025-07-15 23:12:04,133 - [StatusUpdateThread] - INFO - [游戏完成] 玩家 萌新小白(VirtualPlayerID0174), 结果: 物品_5, 订单: None
2025-07-15 23:12:04,133 - [StatusUpdateThread] - DEBUG - 配置文件 config_play.yaml 未发生修改，跳过重新加载
2025-07-15 23:12:04,134 - [StatusUpdateThread] - DEBUG - [游戏完成] 配置文件未修改，跳过配置重新加载
2025-07-15 23:12:04,254 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 5, current_player: {}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:12:04,254 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:12:04,255 - [GameProcessThread] - INFO - [游戏线程] 玩家 东北大碴子(VirtualPlayerID0170) 开始免费游戏，扣除免费次数 (2)
2025-07-15 23:12:04,255 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 东北大碴子(VirtualPlayerID0170) 确保抓中, z_offset_extra=0
2025-07-15 23:12:04,257 - [GameProcessThread] - INFO - 已发送抓取指令到移动服务: ReqID=5c124eec-f56c-4a9d-8734-957b63e4e35d, Player=东北大碴子, Target=4, Z_Offset_Extra=0.0
2025-07-15 23:12:04,257 - [MoveServiceEventReceiver] - DEBUG - 从移动服务收到消息: {'type': 'response', 'command_id': '5c124eec-f56c-4a9d-8734-957b63e4e35d', 'status': 'queued', 'message': '抓取物体 4 指令已加入队列'}
2025-07-15 23:12:04,258 - [GameProcessThread] - INFO - [游戏线程] 玩家 东北大碴子(VirtualPlayerID0170) 抓取指令已发送到移动服务，命令ID: 5c124eec-f56c-4a9d-8734-957b63e4e35d
2025-07-15 23:12:04,258 - [MoveServiceEventReceiver] - INFO - 抓取指令 5c124eec-f56c-4a9d-8734-957b63e4e35d 已被移动服务接受并加入队列
2025-07-15 23:12:04,287 - [DBSyncThread] - INFO - 数据库操作: 添加游戏记录完成，耗时 0.036秒 - 玩家: VirtualPlayerID0174, 结果: 物品_5, 订单: NULL
2025-07-15 23:12:04,287 - [DBSyncThread] - DEBUG - [同步任务] 游戏记录写入成功，更新 tracked_games[VirtualPlayerID0174] = 1
2025-07-15 23:12:04,316 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0170 订单状态: {'free_games_used_this_session': 2}
2025-07-15 23:12:04,322 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0170 的订单相关状态: {'free_games_used_this_session': 2}
2025-07-15 23:12:04,441 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:12:04,444 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:12:04,445 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:12:04,445 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:12:04,446 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:12:04,448 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:12:04,450 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 27
2025-07-15 23:12:04,450 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:12:04,451 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:12:04,471 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:12:04,479 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:12:04,481 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:12:04,481 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: **********.48169
2025-07-15 23:12:04,481 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:12:04,482 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:12:04,482 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:12:04,482 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:12:04,483 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:04,484 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:12:04,484 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:12:04,484 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:12:04,484 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:04,485 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:12:04,774 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': **********.255155, 'target_id': '4', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5c124eec-f56c-4a9d-8734-957b63e4e35d'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:12:04,776 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:12:04,778 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:12:05,183 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 东北大碴子 已完成游戏，总游戏次数: 2
2025-07-15 23:12:05,283 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': **********.255155, 'target_id': '4', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5c124eec-f56c-4a9d-8734-957b63e4e35d'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:12:05,285 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:12:05,287 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:12:05,499 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:12:05,501 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:12:05,501 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:12:05,501 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:12:05,502 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:12:05,502 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:12:05,503 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 28
2025-07-15 23:12:05,503 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:12:05,503 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:12:05,507 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:12:05,534 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:12:05,538 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:12:05,540 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592325.539114
2025-07-15 23:12:05,540 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:12:05,542 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:12:05,543 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:12:05,543 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:12:05,548 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:05,552 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:12:05,553 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:12:05,554 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:12:05,555 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:05,556 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:12:05,799 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': **********.255155, 'target_id': '4', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5c124eec-f56c-4a9d-8734-957b63e4e35d'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:12:05,804 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:12:05,806 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:12:06,312 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': **********.255155, 'target_id': '4', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5c124eec-f56c-4a9d-8734-957b63e4e35d'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:12:06,320 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:12:06,328 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:12:06,569 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:12:06,575 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:12:06,575 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:12:06,575 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:12:06,575 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:12:06,581 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:12:06,581 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 29
2025-07-15 23:12:06,581 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:12:06,588 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:12:06,603 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:12:06,603 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:12:06,603 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:12:06,603 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592326.6030617
2025-07-15 23:12:06,603 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:12:06,603 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:12:06,603 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:12:06,603 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:12:06,603 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:06,603 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:12:06,603 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.1s, 心跳间隔=1s
2025-07-15 23:12:06,603 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:12:06,603 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:06,603 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:12:06,841 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': **********.255155, 'target_id': '4', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5c124eec-f56c-4a9d-8734-957b63e4e35d'}, pending_retry_player: {'entry_tuple': (['', **********.9732003, '粉诱', 'VirtualPlayerID0173', '4', '', '小黄鸭'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0173', 'name': '粉诱', 'start_time': **********.9732003, 'target_id': '4', 'target_object': '小黄鸭', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': 'af195e02-dd3b-43a5-81a1-9ebe66b4a139'}}
2025-07-15 23:12:06,841 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:12:06,850 - [GameProcessThread] - DEBUG - [游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。
2025-07-15 23:12:06,971 - [HealthCheckThread] - DEBUG - [健康检查] 休眠结束
2025-07-15 23:12:06,979 - [HealthCheckThread] - DEBUG - [健康检查] 循环开始
2025-07-15 23:12:06,981 - [MoveServiceEventReceiver] - WARNING - 移动服务事件接收时连接错误（忽略，保持长连）: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-15 23:12:06,981 - [HealthCheckThread] - DEBUG - [健康检查] 距离上次成功请求: 0.4 秒
2025-07-15 23:12:06,994 - [StatusUpdateThread] - ERROR - [移动服务端故障] 移动服务端连接断开，当前玩家将进入pending_retry_player。
2025-07-15 23:12:07,005 - [StatusUpdateThread] - INFO - [重试机制] 由于移动服务断开，移动服务设为未就绪状态
2025-07-15 23:12:06,999 - [HealthCheckThread] - DEBUG - [健康检查] 休眠 30 秒
2025-07-15 23:12:07,006 - [StatusUpdateThread] - WARNING - [重试机制] 玩家 东北大碴子(VirtualPlayerID0170) 已进入pending_retry_player，等待移动服务端恢复后重试。
2025-07-15 23:12:07,354 - [GameProcessThread] - DEBUG - [DEBUG] 当前队列长度: 4, current_player: {}, pending_retry_player: {'entry_tuple': (['', **********.255155, '东北大碴子', 'VirtualPlayerID0170', '4', '', '变形金刚'], 1.0, 10.0, None), 'current_player': {'player_id': 'VirtualPlayerID0170', 'name': '东北大碴子', 'start_time': **********.255155, 'target_id': '4', 'target_object': '变形金刚', 'order_id': None, 'status': 'waiting_move_service', 'move_command_id': '5c124eec-f56c-4a9d-8734-957b63e4e35d'}}
2025-07-15 23:12:07,356 - [GameProcessThread] - DEBUG - [重试机制] 检测到pending_retry_player，但移动服务未就绪，等待服务恢复
2025-07-15 23:12:07,360 - [GameProcessThread] - INFO - [游戏线程] 玩家 柚稚(VirtualPlayerID0171) 开始免费游戏，扣除免费次数 (2)
2025-07-15 23:12:07,362 - [GameProcessThread] - INFO - [游戏线程] 虚拟玩家 柚稚(VirtualPlayerID0171) 确保抓中, z_offset_extra=0
2025-07-15 23:12:07,368 - [GameProcessThread] - ERROR - 发送抓取指令时发生套接字错误: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-15 23:12:07,371 - [GameProcessThread] - INFO - 正在断开与移动服务的连接...
2025-07-15 23:12:07,372 - [GameProcessThread] - INFO - 与移动服务的连接已断开。
2025-07-15 23:12:07,372 - [MoveServiceEventReceiver] - INFO - 移动服务连接断开，将在 1.0 秒后尝试重连...
2025-07-15 23:12:07,372 - [GameProcessThread] - ERROR - [游戏线程] 玩家 柚稚(VirtualPlayerID0171) 发送抓取指令失败，移动服务端可能已断开，当前玩家将进入pending_retry_player，等待服务端恢复后重试。
2025-07-15 23:12:07,372 - [GameProcessThread] - INFO - [重试机制] 由于发送指令失败，移动服务设为未就绪状态
2025-07-15 23:12:07,397 - [DBSyncThread] - INFO - [同步任务] 正在更新玩家 VirtualPlayerID0171 订单状态: {'free_games_used_this_session': 2}
2025-07-15 23:12:07,417 - [DBSyncThread] - DEBUG - 已更新玩家 VirtualPlayerID0171 的订单相关状态: {'free_games_used_this_session': 2}
2025-07-15 23:12:07,616 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:12:07,618 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:12:07,619 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:12:07,620 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:12:07,620 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:12:07,621 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:12:07,621 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 30
2025-07-15 23:12:07,622 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:12:07,622 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:12:07,626 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:12:07,628 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:12:07,629 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:12:07,630 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592327.630862
2025-07-15 23:12:07,630 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:12:07,638 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:12:07,639 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:12:07,640 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:12:07,643 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:07,644 - [MessagePollThread] - DEBUG - [消息流] 响应中包含 0 条消息
2025-07-15 23:12:07,644 - [MessagePollThread] - DEBUG - [消息流] 检查心跳: 未收到消息=True, 距离上次心跳=1.0s, 心跳间隔=1s
2025-07-15 23:12:07,645 - [MessagePollThread] - DEBUG - [消息流] Yield 心跳消息
2025-07-15 23:12:07,645 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:07,647 - [MessagePollThread] - DEBUG - [消息流] 休眠 1 秒
2025-07-15 23:12:07,952 - [DBSyncThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-15 23:12:07,979 - [DBSyncThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 5 个玩家, 成功更新 5 条记录.
2025-07-15 23:12:08,666 - [MainThread] - INFO - 收到终止信号，标志置 False，主线程将退出并在 finally 做清理
2025-07-15 23:12:08,665 - [MessagePollThread] - DEBUG - [消息流] 休眠结束
2025-07-15 23:12:08,670 - [MainThread] - INFO - 进入清理阶段...
2025-07-15 23:12:08,669 - [MessagePollThread] - DEBUG - [消息流] 循环结束
2025-07-15 23:12:08,671 - [MessagePollThread] - DEBUG - [消息流] 循环开始
2025-07-15 23:12:08,671 - [MessagePollThread] - DEBUG - [消息流] 调用 safe_get 请求 URL: http://127.0.0.1:9999/game-da302d82
2025-07-15 23:12:08,672 - [MessagePollThread] - DEBUG - [安全请求] 尝试获取请求锁 (请求: GET http://127.0.0.1:9999/game-da302d82)
2025-07-15 23:12:08,672 - [MessagePollThread] - DEBUG - [安全请求] 获取请求锁成功
2025-07-15 23:12:08,673 - [MessagePollThread] - DEBUG - [安全请求] 请求计数: 31
2025-07-15 23:12:08,674 - [MessagePollThread] - DEBUG - [安全请求] 尝试 1/3
2025-07-15 23:12:08,675 - [MainThread] - INFO - [清理] 开始执行清理操作
2025-07-15 23:12:08,676 - [MessagePollThread] - DEBUG - [安全请求] 调用 requests.get()
2025-07-15 23:12:08,677 - [MainThread] - DEBUG - [清理] 设置 stop_flag['running'] = False
2025-07-15 23:12:08,677 - [MainThread] - INFO - [清理] 阶段1: 停止所有外部输入...
2025-07-15 23:12:08,678 - [MainThread] - DEBUG - [清理] 停止消息获取线程...
2025-07-15 23:12:08,680 - [MessagePollThread] - DEBUG - Starting new HTTP connection (1): 127.0.0.1:9999
2025-07-15 23:12:08,679 - [MainThread] - DEBUG - [消息线程] 尝试停止消息线程
2025-07-15 23:12:08,688 - [MessagePollThread] - DEBUG - http://127.0.0.1:9999 "GET /game-da302d82 HTTP/1.1" 200 None
2025-07-15 23:12:08,688 - [MainThread] - DEBUG - [消息线程] 等待消息线程结束
2025-07-15 23:12:08,689 - [MessagePollThread] - DEBUG - [安全请求] 请求成功，状态码: 200
2025-07-15 23:12:08,689 - [MessagePollThread] - DEBUG - [安全请求] 更新最后成功请求时间: 1752592328.689998
2025-07-15 23:12:08,690 - [MessagePollThread] - DEBUG - [安全请求] 释放请求锁 (成功)
2025-07-15 23:12:08,690 - [MessagePollThread] - DEBUG - [消息流] safe_get 返回: Response object, status: 200
2025-07-15 23:12:08,691 - [MessagePollThread] - DEBUG - [消息流] 尝试解析 JSON 响应
2025-07-15 23:12:08,691 - [MessagePollThread] - DEBUG - [消息流] JSON 解析成功
2025-07-15 23:12:08,693 - [MessagePollThread] - DEBUG - [消息线程] 获取到消息，类型待判断
2025-07-15 23:12:08,694 - [MessagePollThread] - DEBUG - [消息线程] 消息获取线程结束
2025-07-15 23:12:08,694 - [MessagePollThread] - DEBUG - [消息流] 停止健康检查
2025-07-15 23:12:08,695 - [MessagePollThread] - DEBUG - [健康检查] 收到停止信号
2025-07-15 23:12:08,695 - [MessagePollThread] - DEBUG - [健康检查] 等待线程结束
2025-07-15 23:12:08,713 - [DetectionReceiver] - INFO - 已断开检测服务器连接
2025-07-15 23:12:08,718 - [DetectionReceiver] - INFO - 检测数据接收器已停止
2025-07-15 23:12:09,193 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 柚稚 已完成游戏，总游戏次数: 2
2025-07-15 23:12:09,193 - [VirtualPlayerManagerThread] - INFO - [虚拟玩家] 管理器线程已停止。
2025-07-15 23:12:10,422 - [MoveServiceEventReceiver] - ERROR - 移动服务连接被拒绝: localhost:5556
2025-07-15 23:12:10,422 - [MoveServiceEventReceiver] - INFO - 移动服务事件接收线程已停止。
2025-07-15 23:12:10,424 - [MoveServiceEventReceiver] - INFO - 正在断开与移动服务的连接...
2025-07-15 23:12:10,424 - [MoveServiceEventReceiver] - INFO - 与移动服务的连接已断开。
2025-07-15 23:12:10,699 - [MessagePollThread] - INFO - 健康检查线程已停止
2025-07-15 23:12:10,699 - [MainThread] - WARNING - [消息线程] 消息线程未在2秒内结束
2025-07-15 23:12:10,699 - [MainThread] - DEBUG - [消息线程] 消息队列已清空
2025-07-15 23:12:10,699 - [MainThread] - DEBUG - [清理] 停止移动服务客户端...
2025-07-15 23:12:10,699 - [MainThread] - INFO - 正在停止移动服务客户端...
2025-07-15 23:12:10,699 - [MainThread] - INFO - 正在断开与移动服务的连接...
2025-07-15 23:12:10,699 - [MainThread] - INFO - 与移动服务的连接已断开。
2025-07-15 23:12:10,699 - [MainThread] - INFO - 移动服务客户端已停止。
2025-07-15 23:12:10,699 - [MainThread] - DEBUG - [清理] 断开检测服务器连接...
2025-07-15 23:12:10,699 - [MainThread] - INFO - 已断开检测服务器连接
2025-07-15 23:12:10,699 - [MainThread] - INFO - [清理] 阶段2: 停止独立进程...
2025-07-15 23:12:10,699 - [MainThread] - DEBUG - [清理] 关闭GUI通信队列...
2025-07-15 23:12:10,715 - [MainThread] - DEBUG - [清理] 正在终止GUI进程...
2025-07-15 23:12:10,753 - [MainThread] - INFO - [清理] GUI进程已终止。
2025-07-15 23:12:10,753 - [MainThread] - DEBUG - [清理] 停止Web显示进程...
2025-07-15 23:12:11,069 - [MainThread] - INFO - [清理] 阶段3: 等待内部线程结束...
2025-07-15 23:12:11,069 - [MainThread] - DEBUG - [清理] 等待主循环监控线程结束…
2025-07-15 23:12:13,073 - [MainThread] - WARNING - [清理] 主循环监控线程未在2秒内结束
2025-07-15 23:12:13,073 - [MainThread] - INFO - [清理] 阶段4: 最终数据同步和资源释放...
2025-07-15 23:12:13,073 - [MainThread] - DEBUG - [清理] 停止数据库同步管理器...
2025-07-15 23:12:13,073 - [MainThread] - INFO - 停止数据库同步线程
2025-07-15 23:12:13,521 - [MainThread] - INFO - 数据库同步线程已停止
2025-07-15 23:12:13,521 - [MainThread] - ERROR - 更新源 '抓中特效' 可见性时出错: 'NoneType' object has no attribute 'get_current_program_scene'
2025-07-15 23:12:13,521 - [MainThread] - DEBUG - [清理] 强制隐藏 OBS 视频源
2025-07-15 23:12:13,521 - [MainThread] - DEBUG - [清理] 停止健康检查线程...
2025-07-15 23:12:13,521 - [MainThread] - DEBUG - [健康检查] 收到停止信号
2025-07-15 23:12:13,521 - [MainThread] - DEBUG - [健康检查] 等待线程结束
2025-07-15 23:12:15,526 - [MainThread] - INFO - 健康检查线程已停止
2025-07-15 23:12:15,526 - [MainThread] - DEBUG - [清理] 已获取队列锁，执行最终数据同步...
2025-07-15 23:12:15,542 - [MainThread] - INFO - 数据库操作: 更新队列完成，耗时 0.016秒
2025-07-15 23:12:17,556 - [MainThread] - WARNING - 获取快照时未能获取 queue_lock，使用上次快照队列数据
2025-07-15 23:12:17,556 - [MainThread] - DEBUG - [全量同步] 开始同步所有玩家的 comments_after_game 字段
2025-07-15 23:12:17,556 - [MainThread] - INFO - [全量同步] 完成 comments_after_game 更新, 共处理 5 个玩家, 成功更新 5 条记录.
2025-07-15 23:12:17,556 - [MainThread] - INFO - [清理] 最终数据同步已完成
2025-07-15 23:12:17,556 - [MainThread] - DEBUG - [清理] 关闭数据库会话...
2025-07-15 23:12:17,578 - [MainThread] - INFO - 已关闭会话，ID: 1
2025-07-15 23:12:17,578 - [MainThread] - INFO - [清理] 数据库会话已关闭
2025-07-15 23:12:17,578 - [MainThread] - DEBUG - [清理] 队列锁已释放
2025-07-15 23:12:17,578 - [MainThread] - INFO - [清理] 清理操作成功
2025-07-15 23:12:17,656 - [MainThread] - INFO - 程序退出
