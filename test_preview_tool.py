#!/usr/bin/env python3
"""
测试修改后的 Tool_web2OBSpreview.py
"""

import sys
from pathlib import Path

# 确保可以从当前目录导入模块
sys.path.append(str(Path(__file__).parent.absolute()))

try:
    from Tool_web2OBSpreview import OBSPreviewTool
    print("✅ 成功导入 OBSPreviewTool")
    
    # 创建实例
    tool = OBSPreviewTool()
    print("✅ 成功创建 OBSPreviewTool 实例")
    
    # 尝试加载配置
    if tool.load_config():
        print("✅ 成功加载配置文件")
        
        # 检查助手实例是否创建
        if tool.web_display_helper:
            print("✅ 成功创建 WebDisplayProcess 助手实例")
            
            # 检查透视变换矩阵
            if tool.web_display_helper.perspective_transform_matrix is not None:
                print("✅ 透视变换矩阵已初始化")
                print(f"   矩阵形状: {tool.web_display_helper.perspective_transform_matrix.shape}")
            else:
                print("⚠️  透视变换矩阵未初始化（可能是配置问题）")
                
            # 检查 ROI 点
            if tool.web_display_helper.roi_points_transformed_for_js:
                print("✅ ROI 变换点已计算")
                print(f"   ROI 点数量: {len(tool.web_display_helper.roi_points_transformed_for_js)}")
            else:
                print("⚠️  ROI 变换点未计算")
                
            # 测试 HTML 生成
            try:
                html_content = tool.web_display_helper.create_html_content()
                if html_content and len(html_content) > 100:
                    print("✅ HTML 内容生成成功")
                    print(f"   HTML 长度: {len(html_content)} 字符")
                else:
                    print("❌ HTML 内容生成失败或内容过短")
            except Exception as e:
                print(f"❌ HTML 生成出错: {e}")
                
            # 测试抓取区域数据生成
            try:
                grab_data = tool.generate_dummy_grab_area_data()
                if grab_data and 'data' in grab_data:
                    objects = grab_data['data'].get('objects', [])
                    roi_points = grab_data['data'].get('roi_boundary_points_transformed', [])
                    print(f"✅ 抓取区域数据生成成功")
                    print(f"   模拟物体数量: {len(objects)}")
                    print(f"   ROI 边界点数量: {len(roi_points)}")
                else:
                    print("❌ 抓取区域数据生成失败")
            except Exception as e:
                print(f"❌ 抓取区域数据生成出错: {e}")
                
        else:
            print("❌ WebDisplayProcess 助手实例创建失败")
    else:
        print("❌ 配置文件加载失败")
        
except ImportError as e:
    print(f"❌ 导入失败: {e}")
except Exception as e:
    print(f"❌ 测试过程中出错: {e}")
    import traceback
    traceback.print_exc()

print("\n🔚 测试完成")
