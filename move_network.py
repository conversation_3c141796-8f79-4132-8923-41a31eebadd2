# 网络通信模块：处理检测服务客户端和游戏服务端
import socket
import json
import threading
import time
import queue
import random
import traceback
import datetime

from Move_Coordinate import normalized_to_physical_coordinates
from Move_Hardware import check_gripper_status_network


def get_timestamp_network():
    """获取当前时间戳字符串，格式为HH:MM:SS.mmm"""
    now = datetime.datetime.now()
    return now.strftime("%H:%M:%S.") + f"{now.microsecond//1000:03d}"


# 新增：全局变量，保存最新的检测帧率
latest_detection_fps = None
latest_detection_fps_lock = threading.Lock()


class DetectionClient:
    """检测服务客户端，用于接收推送的检测结果"""
    def __init__(self, host='localhost', port=5555, tank_dims=None):
        self.host = host
        self.port = port
        self.socket = None
        self.running = False
        self.receive_thread = None
        self.tank_dimensions = tank_dims
        
        if self.tank_dimensions is None:
            raise Exception("DetectionClient 初始化失败：未提供鱼缸尺寸 (tank_dimensions)")
    
    def connect_and_subscribe(self):
        """连接到检测服务并订阅检测结果"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            # 设置连接超时
            self.socket.settimeout(5.0)
            self.socket.connect((self.host, self.port))
            
            # 发送订阅请求
            subscribe_request = json.dumps({"command": "subscribe"}) + '\n'
            self.socket.sendall(subscribe_request.encode('utf-8'))
            
            # 接收订阅确认，使用超时机制
            response_data = ""
            self.socket.settimeout(15.0)  # 设置15秒超时
            try:
                while '\n' not in response_data:
                    chunk = self.socket.recv(1024)
                    if not chunk:
                        print(f"[{get_timestamp_network()}] 订阅检测服务时连接被关闭 (recv返回空)")
                        if self.socket:  # 添加检查确保socket不是None
                            self.socket.close()
                            self.socket = None
                        return False
                    response_data += chunk.decode('utf-8', errors='ignore')
                
                # 成功接收后清除超时
                if self.socket:  # 添加检查确保socket不是None
                    self.socket.settimeout(None)  
                
                try:
                    response = json.loads(response_data.strip())
                    if response.get('status') == 'subscribed':
                        print(f"[{get_timestamp_network()}] 成功连接并订阅检测服务: {response.get('message', '')}")
                        return True
                    else:
                        print(f"[{get_timestamp_network()}] 订阅失败: {response.get('message', '')}")
                        if self.socket:
                            self.socket.close()
                            self.socket = None
                        return False
                except json.JSONDecodeError as je:
                    print(f"[{get_timestamp_network()}] 订阅响应不是有效的JSON格式: {je}")
                    if self.socket:
                        self.socket.close()
                        self.socket = None
                    return False
                    
            except socket.timeout:
                print(f"[{get_timestamp_network()}] 错误：等待检测服务订阅响应超时(15秒)。请检查检测服务是否正确发送了带换行符的JSON响应。")
                if self.socket:  # 添加检查确保socket不是None
                    self.socket.close()
                    self.socket = None
                return False
            except Exception as e:
                print(f"[{get_timestamp_network()}] 接收订阅响应时发生错误: {e}")
                if self.socket:
                    self.socket.close()
                    self.socket = None
                return False
                
        except socket.timeout:
            print(f"[{get_timestamp_network()}] 连接检测服务超时(5秒): {self.host}:{self.port}")
            if self.socket:
                self.socket.close()
                self.socket = None
            return False
        except Exception as e:
            print(f"[{get_timestamp_network()}] 连接检测服务失败: {e}")
            if self.socket:
                self.socket.close()
                self.socket = None
            return False
    
    def start_receiving(self, global_program_stop_event, objects_lock, latest_objects_ref):
        """开始接收检测结果推送"""
        if not self.connect_and_subscribe():
            return False
            
        self.running = True
        self.receive_thread = threading.Thread(
            target=self._receive_loop, 
            args=(global_program_stop_event, objects_lock, latest_objects_ref)
        )
        self.receive_thread.daemon = True
        self.receive_thread.start()
        return True
    
    def _receive_loop(self, global_program_stop_event, objects_lock, latest_objects_ref):
        """接收检测结果的循环"""
        buffer = ""
        
        while self.running and not global_program_stop_event.is_set():
            try:
                # 添加检查确保socket不是None
                if self.socket is None:
                    if self.running and not global_program_stop_event.is_set():
                        print(f"[{get_timestamp_network()}] 检测服务socket未连接，尝试重新连接...")
                        time.sleep(5)
                        if not self.connect_and_subscribe():
                            continue
                    else:
                        break
                
                data = self.socket.recv(4096).decode('utf-8')
                if not data:
                    print(f"[{get_timestamp_network()}] 检测服务连接断开")
                    self.socket.close()
                    self.socket = None
                    if self.running and not global_program_stop_event.is_set():
                        print(f"[{get_timestamp_network()}] 尝试重新连接检测服务...")
                        time.sleep(5)
                        self.connect_and_subscribe()
                    continue
                
                buffer += data
                
                # 处理可能的多个JSON消息
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    if line.strip():
                        try:
                            detection_data = json.loads(line)
                            self._process_detection_results(detection_data, objects_lock, latest_objects_ref)
                        except json.JSONDecodeError as e:
                            print(f"[{get_timestamp_network()}] 解析检测结果JSON失败: {e}")
                            
            except socket.timeout:
                continue
            except socket.error as e:
                print(f"[{get_timestamp_network()}] Socket错误: {e}")
                if self.socket:
                    self.socket.close()
                    self.socket = None
                if self.running and not global_program_stop_event.is_set():
                    print(f"[{get_timestamp_network()}] 尝试重新连接检测服务...")
                    time.sleep(5)
                    self.connect_and_subscribe()
                else:
                    break
            except Exception as e:
                if self.running and not global_program_stop_event.is_set():
                    print(f"[{get_timestamp_network()}] 接收检测结果时出错: {e}")
                    time.sleep(1)
                else:
                    break
        
        print(f"[{get_timestamp_network()}] 检测结果接收线程结束")
    
    def _process_detection_results(self, detection_data, objects_lock, latest_objects_ref):
        """处理接收到的检测结果，转换归一化比例坐标为物理坐标"""
        
        if detection_data.get('status') != 'data_updated':
            print(f"[{get_timestamp_network()}] 接收到非成功状态的检测数据: {detection_data.get('status')}")
            return
            
        data = detection_data.get('data', {})
        
        # 新增：提取并保存FPS
        global latest_detection_fps, latest_detection_fps_lock
        fps = data.get('fps', None)
        # print(f"[DEBUG] 收到FPS: {fps}")  # 检测FPS收到
        if fps is not None:
            with latest_detection_fps_lock:
                latest_detection_fps = fps

        if not data:
            print(f"[{get_timestamp_network()}] 警告: 检测数据中 'data' 字段为空或不存在")
            return

        raw_objects = data.get('objects', [])
        if not isinstance(raw_objects, list):
            print(f"[{get_timestamp_network()}] 警告: 'objects' 字段不是列表: {type(raw_objects)}")
            raw_objects = []
        
        physical_objects = []
        
        for raw_obj in raw_objects:
            # 获取归一化比例坐标
            center_normalized_prop = raw_obj.get("center_normalized_prop")
            
            if center_normalized_prop is None or not (isinstance(center_normalized_prop, list) and len(center_normalized_prop) == 2):
                print(f"[{get_timestamp_network()}] 警告: 物体 {raw_obj.get('track_number', 'N/A')} 的 center_normalized_prop 字段缺失或格式不正确。跳过此物体")
                continue

            norm_x, norm_y = center_normalized_prop
            
            if norm_x is None or norm_y is None:
                print(f"[{get_timestamp_network()}] 警告: 物体 {raw_obj.get('track_number', 'N/A')} 的归一化坐标包含None值。跳过此物体")
                continue

            # 使用新的转换函数和存储的鱼缸尺寸
            physical_x, physical_y = normalized_to_physical_coordinates(
                norm_x, norm_y, self.tank_dimensions
            )

            if physical_x is None or physical_y is None:
                print(f"[{get_timestamp_network()}] 警告: 物体 {raw_obj.get('track_number', 'N/A')} 坐标转换失败。跳过此物体")
                continue
            
            # 创建包含物理坐标的物体信息
            physical_obj = {
                "track_number": raw_obj.get("track_number"),
                "class_id": raw_obj.get("class_id"),
                "class_name": raw_obj.get("class_name"),
                "center_abs_pixel": raw_obj.get("center_abs_pixel"),
                "center_normalized_prop": center_normalized_prop,
                "physical_coords": [physical_x, physical_y],
                "confidence": raw_obj.get("confidence"),
                "bounding_box_xyxy_abs": raw_obj.get("bounding_box_xyxy_abs"),
                "bounding_box_xyxy_normalized_prop": raw_obj.get("bounding_box_xyxy_normalized_prop")
            }
            physical_objects.append(physical_obj)
        
        # 更新全局物体列表
        with objects_lock:
            latest_objects_ref[:] = physical_objects

    def stop(self):
        """停止接收检测结果"""
        self.running = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
        
        if self.receive_thread:
            self.receive_thread.join(timeout=2.0)


class GameServiceServer:
    """游戏主程序服务端，接受游戏程序的指令"""
    def __init__(self, host='localhost', port=5556):
        self.host = host
        self.port = port
        self.server_socket = None
        self.running = False
        self.clients = []  # 新增：活跃客户端列表

    def start(self, global_program_stop_event, command_queue, global_serial, serial_lock):
        """启动服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            self.server_socket.settimeout(0.1)

            self.running = True
            print(f"[{get_timestamp_network()}] 游戏服务端已启动，监听地址: {self.host}:{self.port}")

            while self.running and not global_program_stop_event.is_set():
                try:
                    client_socket, addr = self.server_socket.accept()
                    # 为每个客户端启动一个处理线程
                    client_thread = threading.Thread(
                        target=self._handle_game_client,
                        args=(client_socket, addr, global_program_stop_event, command_queue, global_serial, serial_lock)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                except socket.timeout:
                    pass
                except Exception as e:
                    if self.running and not global_program_stop_event.is_set():
                        print(f"[{get_timestamp_network()}] 游戏服务器循环错误: {e}")
                    break

        except Exception as e:
            print(f"[{get_timestamp_network()}] 启动游戏服务器失败: {e}")
        finally:
            if self.server_socket:
                self.server_socket.close()
                print(f"[{get_timestamp_network()}] 游戏服务器已关闭")

    def _handle_game_client(self, client_socket, addr, global_program_stop_event, command_queue, global_serial, serial_lock):
        # 新增：维护活跃客户端列表
        self.clients.append(client_socket)
        try:
            handle_game_client(self, client_socket, addr, global_program_stop_event, command_queue, global_serial, serial_lock)
        finally:
            try:
                client_socket.close()
            except:
                pass
            # 新增：移除已关闭的socket
            if client_socket in self.clients:
                self.clients.remove(client_socket)
            print(f"[{get_timestamp_network()}] 游戏客户端已断开: {addr}")

    def stop(self):
        """停止服务器"""
        self.running = False


def safe_send_event(client_socket, event_dict, server_instance=None):
    """发送事件前检测socket是否有效"""
    if not client_socket:
        return
    # 检查 socket 是否已关闭
    if hasattr(client_socket, '_closed') and client_socket._closed:
        print(f"[{get_timestamp_network()}] 发送事件失败: 客户端socket已关闭")
        return
    # 检查是否还在活跃列表
    if server_instance and hasattr(server_instance, 'clients'):
        if client_socket not in server_instance.clients:
            print(f"[{get_timestamp_network()}] 发送事件失败: 客户端socket已断开（不在活跃列表）")
            return
    try:
        event_msg = json.dumps(event_dict) + '\n'
        client_socket.sendall(event_msg.encode('utf-8'))
    except Exception as e:
        print(f"[{get_timestamp_network()}] 发送事件失败: {e}")


def select_target_object(requested_id, objects, event_socket, request_id, server_instance=None):
    """选择目标物体，如果指定编号不存在则随机选择"""
    if not objects:
        if event_socket:
            err_event = {
                'type': 'event',
                'request_id': request_id,
                'event_name': 'operation_error',
                'data': {'object_id': requested_id, 'error_message': '当前未检测到任何物体'}
            }
            safe_send_event(event_socket, err_event, server_instance)
        return None

    # 检查指定编号是否存在
    for obj in objects:
        if obj.get("track_number") == requested_id:
            print(f"[{get_timestamp_network()}] 找到指定物体编号 {requested_id}")
            return requested_id

    # 指定编号不存在，随机选择一个
    available_objects = [obj for obj in objects if obj.get("track_number") is not None]
    if not available_objects:
        if event_socket:
            err_event = {
                'type': 'event',
                'request_id': request_id,
                'event_name': 'operation_error',
                'data': {'object_id': requested_id, 'error_message': '未找到有效编号的物体'}
            }
            safe_send_event(event_socket, err_event, server_instance)
        return None

    selected_obj = random.choice(available_objects)
    final_id = selected_obj.get("track_number")
    print(f"[{get_timestamp_network()}] 未找到指定物体编号 {requested_id}，随机选择物体编号 {final_id}")

    # 发送物体选择变更事件
    if event_socket:
        change_event = {
            'type': 'event',
            'request_id': request_id,
            'event_name': 'object_selection_changed',
            'data': {
                'requested_object_id': requested_id,
                'actual_object_id': final_id,
                'reason': '指定编号不存在，自动随机选择',
                'message': f'原请求物体 {requested_id} 不存在，已自动选择物体 {final_id}'
            }
        }
        safe_send_event(event_socket, change_event, server_instance)

    return final_id


def start_detection_client(tank_dims, config, global_program_stop_event, objects_lock, latest_objects_ref):
    """启动检测服务客户端，如果检测服务端未启动则等待重试"""
    detection_config = config.get('detection_service', {})
    host = detection_config.get('host', 'localhost')
    port = detection_config.get('port', 5555)

    detection_client = DetectionClient(host, port, tank_dims=tank_dims)

    # 持续尝试连接检测服务端，直到成功或程序停止
    retry_count = 0
    while not global_program_stop_event.is_set():
        retry_count += 1
        print(f"[{get_timestamp_network()}] 尝试连接检测服务端 (第{retry_count}次): {host}:{port}")

        if detection_client.start_receiving(global_program_stop_event, objects_lock, latest_objects_ref):
            print(f"[{get_timestamp_network()}] 检测结果接收服务已启动")
            return detection_client
        else:
            if global_program_stop_event.is_set():
                break
            print(f"[{get_timestamp_network()}] 检测服务端未启动，2秒后重试...")
            time.sleep(2)

    print(f"[{get_timestamp_network()}] 程序停止，取消检测服务端连接尝试")
    return None



def handle_game_client(server_instance, client_socket, addr, global_program_stop_event, command_queue, global_serial, serial_lock):
    """处理游戏客户端连接"""
    print(f"[{get_timestamp_network()}] 游戏客户端已连接: {addr}")
    try:
        client_socket.settimeout(5.0)
        while server_instance.running and not global_program_stop_event.is_set():
            try:
                data = client_socket.recv(4096).decode('utf-8')
                if not data:
                    break
                # 处理可能的多个JSON消息
                messages = data.strip().split('\n')
                for msg in messages:
                    if not msg.strip():
                        continue
                    try:
                        request = json.loads(msg)
                        response = process_game_request(request, client_socket, command_queue, global_serial, serial_lock, server_instance)
                        if response and not global_program_stop_event.is_set():
                            response_msg = json.dumps(response) + '\n'
                            client_socket.sendall(response_msg.encode('utf-8'))
                    except json.JSONDecodeError as e:
                        if not global_program_stop_event.is_set():
                            response = {
                                'type': 'response',
                                'status': 'error',
                                'message': f'无效的JSON请求: {str(e)}'
                            }
                            response_msg = json.dumps(response) + '\n'
                            client_socket.sendall(response_msg.encode('utf-8'))
            except socket.timeout:
                continue
            except Exception as e:
                if not global_program_stop_event.is_set():
                    print(f"[{get_timestamp_network()}] 处理游戏客户端数据时出错 ({addr}): {e}")
                break
    except Exception as e:
        print(f"[{get_timestamp_network()}] 游戏客户端连接错误 ({addr}): {e}")
    finally:
        try:
            client_socket.close()
        except:
            pass
        # 移除已关闭的socket由GameServiceServer._handle_game_client处理
        print(f"[{get_timestamp_network()}] 游戏客户端已断开: {addr}")


def process_game_request(request, client_socket, command_queue, global_serial, serial_lock, server_instance=None):
    """处理游戏客户端请求"""
    request_type = request.get('type', '')
    if request_type == 'command':
        request['client_socket_for_event'] = client_socket
        request['server_instance'] = server_instance  # 传递server_instance
        return handle_command_request(request, command_queue, server_instance)
    elif request_type == 'query':
        return handle_query_request(request, global_serial, serial_lock)
    else:
        return {
            'type': 'response',
            'status': 'error',
            'message': f'未知请求类型: {request_type}'
        }


def handle_command_request(request, command_queue, server_instance=None):
    """处理指令请求（如移动指令）"""
    action = request.get('action', '')
    command_id = request.get('command_id', 'unknown')

    if action == 'process_movement':
        params = request.get('params', {})
        command_item = {
            'type': 'movement',
            'params': params,
            'request_id': command_id,
            'client_socket': None,
            'server_instance': server_instance
        }
        try:
            command_queue.put(command_item, timeout=1.0)
            return {
                'type': 'response',
                'command_id': command_id,
                'status': 'queued',
                'message': '移动指令已加入队列'
            }
        except queue.Full:
            return {
                'type': 'response',
                'command_id': command_id,
                'status': 'error',
                'message': '指令队列已满'
            }
    elif action == 'pick_object_by_id':
        params = request.get('params', {})
        object_id = params.get('object_id')
        if object_id is None:
            return {
                'type': 'response',
                'command_id': command_id,
                'status': 'error',
                'message': '缺少 object_id 参数'
            }
        command_item = {
            'type': 'pick_object',
            'params': params,
            'request_id': command_id,
            'client_socket': request.get('client_socket_for_event'),
            'server_instance': server_instance
        }
        try:
            command_queue.put(command_item, timeout=1.0)
            return {
                'type': 'response',
                'command_id': command_id,
                'status': 'queued',
                'message': f'抓取物体 {object_id} 指令已加入队列'
            }
        except queue.Full:
            return {
                'type': 'response',
                'command_id': command_id,
                'status': 'error',
                'message': '指令队列已满，无法加入抓取指令'
            }
    else:
        return {
            'type': 'response',
            'command_id': command_id,
            'status': 'error',
            'message': f'未知指令动作: {action}'
        }


def handle_query_request(request, global_serial, serial_lock):
    """处理查询请求（如爪子状态查询）"""
    action = request.get('action', '')
    
    if action == 'get_gripper_status':
        try:
            # 直接调用爪子状态查询函数，避免嵌套锁
            gripper_closed = check_gripper_status_network(global_serial, serial_lock)

            return {
                'type': 'response',
                'query_id': request.get('query_id'),
                'status': 'success',
                'data': {
                    'object_picked': gripper_closed
                },
                'message': '爪子状态查询成功'
            }

        except Exception as e:
            return {
                'type': 'response',
                'query_id': request.get('query_id'),
                'status': 'error',
                'message': f'查询爪子状态失败: {str(e)}'
            }
    else:
        return {
            'type': 'response',
            'query_id': request.get('query_id'),
            'status': 'error',
            'message': f'未知查询动作: {action}'
        }
